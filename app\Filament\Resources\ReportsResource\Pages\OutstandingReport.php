<?php

namespace App\Filament\Resources\ReportsResource\Pages;

use App\Filament\Resources\ReportsResource;
use App\Services\ReportingService;
use App\Models\Currency;
use Filament\Resources\Pages\Page;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Actions\Action;
use Illuminate\Support\Facades\Response;

class OutstandingReport extends Page
{
    protected static string $resource = ReportsResource::class;
    
    protected static string $view = 'filament.resources.reports.pages.outstanding-report';
    
    public ?array $data = [];
    
    public function mount(): void
    {
        $this->form->fill([
            'currency_id' => null,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('currency_id')
                    ->label('Currency Filter')
                    ->options(Currency::pluck('name', 'id'))
                    ->placeholder('All Currencies')
                    ->searchable(),
            ])
            ->statePath('data')
            ->live();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('generate_report')
                ->label('Generate Report')
                ->icon('heroicon-o-arrow-path')
                ->action('generateReport'),
                
            Action::make('export_pdf')
                ->label('Export PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->action('exportPdf'),
                
            Action::make('export_excel')
                ->label('Export Excel')
                ->icon('heroicon-o-table-cells')
                ->action('exportExcel'),
                
            Action::make('back_to_reports')
                ->label('Back to Reports')
                ->icon('heroicon-o-arrow-left')
                ->url(fn () => ReportsResource::getUrl('index'))
                ->color('gray'),
        ];
    }

    public function generateReport(): void
    {
        $this->validate();
        
        $currencyId = $this->data['currency_id'] ?? null;
        
        $reportingService = app(ReportingService::class);
        $this->reportData = $reportingService->getOutstandingAnalytics($currencyId);
        
        $this->dispatch('report-generated');
    }

    public function exportPdf()
    {
        $this->generateReport();

        $pdf = app('dompdf.wrapper');
        $pdf->loadView('reports.outstanding-pdf', [
            'data' => $this->reportData,
            'filters' => $this->data,
        ]);

        return $pdf->download('outstanding-report-' . now()->format('Y-m-d') . '.pdf');
    }

    public function exportExcel()
    {
        $this->generateReport();

        $csvData = $this->prepareCsvData();

        return Response::streamDownload(function () use ($csvData) {
            echo $csvData;
        }, 'outstanding-report-' . now()->format('Y-m-d') . '.csv', [
            'Content-Type' => 'text/csv',
        ]);
    }

    protected function prepareCsvData(): string
    {
        $csv = "Outstanding Invoices Report\n";
        $csv .= "Generated: " . now()->format('Y-m-d H:i:s') . "\n\n";
        
        $csv .= "Summary\n";
        $csv .= "Total Outstanding,{$this->reportData['total_outstanding']}\n";
        $csv .= "Overdue Amount,{$this->reportData['overdue_amount']}\n";
        $csv .= "Invoice Count,{$this->reportData['invoice_count']}\n";
        $csv .= "Overdue Count,{$this->reportData['overdue_count']}\n\n";
        
        $csv .= "Outstanding by Client\n";
        $csv .= "Client Name,Outstanding Amount,Invoice Count,Overdue Amount\n";
        foreach ($this->reportData['outstanding_by_client'] as $client) {
            $csv .= "\"{$client['client_name']}\",{$client['outstanding_amount']},{$client['invoice_count']},{$client['overdue_amount']}\n";
        }
        
        return $csv;
    }

    protected function getViewData(): array
    {
        if (!isset($this->reportData)) {
            $this->generateReport();
        }
        
        return [
            'reportData' => $this->reportData ?? [],
            'filters' => $this->data,
        ];
    }

    public $reportData = [];
}
