<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Client Analysis Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #7c3aed;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .summary-section {
            margin-bottom: 25px;
        }
        .summary-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .summary-item {
            display: table-cell;
            width: 25%;
            padding: 10px;
            border: 1px solid #ddd;
            text-align: center;
            vertical-align: top;
        }
        .summary-item h3 {
            margin: 0 0 5px 0;
            font-size: 14px;
            color: #666;
        }
        .summary-item .value {
            font-size: 18px;
            font-weight: bold;
            color: #7c3aed;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0 10px 0;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .risk-low {
            color: #059669;
            font-weight: bold;
        }
        .risk-medium {
            color: #f59e0b;
            font-weight: bold;
        }
        .risk-high {
            color: #dc2626;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .alert {
            background-color: #fef3c7;
            border: 1px solid #fcd34d;
            color: #92400e;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Client Analysis Report</h1>
        <p>Generated on: {{ now()->format('F j, Y \a\t g:i A') }}</p>
        <p>Comprehensive client behavior and risk analysis</p>
    </div>

    <!-- High Risk Alert -->
    @if(($data['high_risk_clients'] ?? 0) > 0)
    <div class="alert">
        <strong>Risk Alert:</strong> {{ $data['high_risk_clients'] }} client(s) have been identified as high-risk and require immediate attention.
    </div>
    @endif

    <!-- Summary Section -->
    <div class="summary-section">
        <div class="section-title">Client Portfolio Summary</div>
        <div class="summary-grid">
            <div class="summary-item">
                <h3>Total Clients</h3>
                <div class="value">{{ number_format($data['total_clients'] ?? 0) }}</div>
            </div>
            <div class="summary-item">
                <h3>High Risk Clients</h3>
                <div class="value">{{ number_format($data['high_risk_clients'] ?? 0) }}</div>
            </div>
            <div class="summary-item">
                <h3>Avg Payment Time</h3>
                <div class="value">{{ number_format($data['average_payment_time'] ?? 0, 1) }} days</div>
            </div>
            <div class="summary-item">
                <h3>Top Client Revenue</h3>
                <div class="value">{{ getCurrencyAmount($data['top_client_revenue'] ?? 0, true) }}</div>
            </div>
        </div>
    </div>

    <!-- Client Risk Distribution -->
    <div class="section-title">Risk Distribution Analysis</div>
    <table>
        <thead>
            <tr>
                <th>Risk Level</th>
                <th class="text-center">Client Count</th>
                <th class="text-right">Total Revenue</th>
                <th class="text-right">Percentage</th>
                <th>Characteristics</th>
            </tr>
        </thead>
        <tbody>
            @php
                $lowRisk = collect($data['client_analysis'] ?? [])->where('risk_score', '<', 40)->count();
                $mediumRisk = collect($data['client_analysis'] ?? [])->where('risk_score', '>=', 40)->where('risk_score', '<', 70)->count();
                $highRisk = collect($data['client_analysis'] ?? [])->where('risk_score', '>=', 70)->count();
                $totalClients = $lowRisk + $mediumRisk + $highRisk;
            @endphp
            <tr>
                <td><span class="risk-low">Low Risk (0-39)</span></td>
                <td class="text-center">{{ $lowRisk }}</td>
                <td class="text-right">{{ getCurrencyAmount(collect($data['client_analysis'] ?? [])->where('risk_score', '<', 40)->sum('total_paid'), true) }}</td>
                <td class="text-right">{{ $totalClients > 0 ? number_format(($lowRisk / $totalClients) * 100, 1) : 0 }}%</td>
                <td>Reliable payment history, low overdue rates</td>
            </tr>
            <tr>
                <td><span class="risk-medium">Medium Risk (40-69)</span></td>
                <td class="text-center">{{ $mediumRisk }}</td>
                <td class="text-right">{{ getCurrencyAmount(collect($data['client_analysis'] ?? [])->where('risk_score', '>=', 40)->where('risk_score', '<', 70)->sum('total_paid'), true) }}</td>
                <td class="text-right">{{ $totalClients > 0 ? number_format(($mediumRisk / $totalClients) * 100, 1) : 0 }}%</td>
                <td>Occasional late payments, moderate risk</td>
            </tr>
            <tr>
                <td><span class="risk-high">High Risk (70-100)</span></td>
                <td class="text-center">{{ $highRisk }}</td>
                <td class="text-right">{{ getCurrencyAmount(collect($data['client_analysis'] ?? [])->where('risk_score', '>=', 70)->sum('total_paid'), true) }}</td>
                <td class="text-right">{{ $totalClients > 0 ? number_format(($highRisk / $totalClients) * 100, 1) : 0 }}%</td>
                <td>Frequent overdue payments, high risk</td>
            </tr>
        </tbody>
    </table>

    <!-- Detailed Client Analysis -->
    @if(isset($data['client_analysis']) && !empty($data['client_analysis']))
    <div class="section-title">Detailed Client Analysis</div>
    <table>
        <thead>
            <tr>
                <th>Client Name</th>
                <th class="text-right">Total Invoiced</th>
                <th class="text-right">Total Paid</th>
                <th class="text-right">Payment Rate</th>
                <th class="text-center">Overdue Count</th>
                <th class="text-right">Avg Payment Time</th>
                <th class="text-center">Risk Score</th>
                <th class="text-center">Risk Level</th>
            </tr>
        </thead>
        <tbody>
            @foreach(array_slice($data['client_analysis'], 0, 25) as $client)
            <tr>
                <td>{{ $client['client_name'] }}</td>
                <td class="text-right">{{ getCurrencyAmount($client['total_invoiced'], true) }}</td>
                <td class="text-right">{{ getCurrencyAmount($client['total_paid'], true) }}</td>
                <td class="text-right">{{ number_format($client['payment_rate'], 1) }}%</td>
                <td class="text-center">{{ $client['overdue_count'] }}</td>
                <td class="text-right">{{ number_format($client['average_payment_time'], 1) }} days</td>
                <td class="text-center">{{ $client['risk_score'] }}</td>
                <td class="text-center">
                    @if($client['risk_score'] >= 70)
                        <span class="risk-high">High</span>
                    @elseif($client['risk_score'] >= 40)
                        <span class="risk-medium">Medium</span>
                    @else
                        <span class="risk-low">Low</span>
                    @endif
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <!-- High Risk Clients Detail -->
    @if(collect($data['client_analysis'] ?? [])->where('risk_score', '>=', 70)->count() > 0)
    <div class="section-title">High Risk Clients - Immediate Action Required</div>
    <table>
        <thead>
            <tr>
                <th>Client Name</th>
                <th class="text-right">Outstanding Amount</th>
                <th class="text-center">Overdue Invoices</th>
                <th class="text-right">Days Since Last Payment</th>
                <th>Recommended Action</th>
            </tr>
        </thead>
        <tbody>
            @foreach(collect($data['client_analysis'] ?? [])->where('risk_score', '>=', 70)->take(10) as $client)
            <tr>
                <td>{{ $client['client_name'] }}</td>
                <td class="text-right">{{ getCurrencyAmount($client['overdue_amount'] ?? 0, true) }}</td>
                <td class="text-center">{{ $client['overdue_count'] }}</td>
                <td class="text-right">{{ number_format($client['average_payment_time'], 0) }}</td>
                <td>
                    @if($client['overdue_count'] > 3)
                        Consider credit hold and collection agency
                    @elseif($client['overdue_count'] > 1)
                        Implement payment plan and closer monitoring
                    @else
                        Send payment reminder and follow up
                    @endif
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <!-- Client Performance Trends -->
    <div class="section-title">Performance Insights</div>
    <table>
        <thead>
            <tr>
                <th>Metric</th>
                <th class="text-right">Value</th>
                <th>Analysis</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Average Client Value</td>
                <td class="text-right">{{ getCurrencyAmount(collect($data['client_analysis'] ?? [])->avg('total_paid'), true) }}</td>
                <td>{{ collect($data['client_analysis'] ?? [])->avg('total_paid') > 5000 ? 'High-value client base' : 'Opportunity to increase client value' }}</td>
            </tr>
            <tr>
                <td>Payment Reliability</td>
                <td class="text-right">{{ number_format(collect($data['client_analysis'] ?? [])->avg('payment_rate'), 1) }}%</td>
                <td>{{ collect($data['client_analysis'] ?? [])->avg('payment_rate') > 85 ? 'Excellent payment reliability' : 'Room for improvement in collections' }}</td>
            </tr>
            <tr>
                <td>Risk Concentration</td>
                <td class="text-right">{{ $totalClients > 0 ? number_format(($highRisk / $totalClients) * 100, 1) : 0 }}%</td>
                <td>{{ ($totalClients > 0 && ($highRisk / $totalClients) > 0.2) ? 'High risk concentration - diversify client base' : 'Acceptable risk distribution' }}</td>
            </tr>
        </tbody>
    </table>

    <div class="footer">
        <p>This report was generated automatically by the Invoice Management System</p>
        <p>{{ config('app.name') }} - Client Analysis & Risk Assessment</p>
    </div>
</body>
</html>
