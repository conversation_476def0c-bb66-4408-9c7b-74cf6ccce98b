<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center space-x-2">
                <svg class="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Outstanding Invoices</span>
            </div>
        </x-slot>
        
        <x-slot name="headerEnd">
            <div class="flex items-center space-x-4 text-sm text-gray-500">
                @if($hasData)
                    <div class="flex items-center space-x-1">
                        <span class="w-2 h-2 bg-orange-500 rounded-full"></span>
                        <span>{{ count($statusBreakdown) }} Categories</span>
                    </div>
                @else
                    <div class="flex items-center space-x-1">
                        <span class="w-2 h-2 bg-gray-400 rounded-full"></span>
                        <span>No Outstanding</span>
                    </div>
                @endif
            </div>
        </x-slot>

        <div class="space-y-4">
            <!-- KPI Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-red-600 dark:text-red-400">Total Outstanding</p>
                            <p class="text-xl font-bold text-red-900 dark:text-red-100">
                                {{ getCurrencyAmount($totalOutstanding, true) }}
                            </p>
                        </div>
                        <div class="p-2 bg-red-500 rounded-lg">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-orange-600 dark:text-orange-400">Overdue Amount</p>
                            <p class="text-xl font-bold text-orange-900 dark:text-orange-100">
                                {{ getCurrencyAmount($overdueAmount, true) }}
                            </p>
                        </div>
                        <div class="p-2 bg-orange-500 rounded-lg">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-yellow-600 dark:text-yellow-400">Unpaid Count</p>
                            <p class="text-xl font-bold text-yellow-900 dark:text-yellow-100">
                                {{ number_format($unpaidCount) }}
                            </p>
                        </div>
                        <div class="p-2 bg-yellow-500 rounded-lg">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-red-600 dark:text-red-400">Overdue Count</p>
                            <p class="text-xl font-bold text-red-900 dark:text-red-100">
                                {{ number_format($overdueCount) }}
                            </p>
                        </div>
                        <div class="p-2 bg-red-500 rounded-lg">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chart and Status Breakdown -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Chart Container -->
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Status Distribution</h3>
                    @if($hasData)
                        <div class="relative" style="height: 300px;">
                            <canvas id="outstandingChart" class="w-full h-full"></canvas>
                        </div>
                    @else
                        <div class="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400">
                            <svg class="w-16 h-16 mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <h3 class="text-lg font-medium mb-2">No Outstanding Invoices</h3>
                            <p class="text-center max-w-md">
                                Great! All invoices are paid or there are no invoices yet.
                            </p>
                            @if(isset($error))
                                <p class="text-red-500 text-sm mt-2">Error: {{ $error }}</p>
                            @endif
                        </div>
                    @endif
                </div>

                <!-- Status Breakdown List -->
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Breakdown Details</h3>
                    @if($hasData)
                        <div class="space-y-3">
                            @foreach($statusBreakdown as $status => $amount)
                                @php
                                    $statusColors = [
                                        'overdue' => ['bg' => 'bg-red-100 dark:bg-red-900/20', 'text' => 'text-red-800 dark:text-red-200', 'dot' => 'bg-red-500'],
                                        'unpaid' => ['bg' => 'bg-yellow-100 dark:bg-yellow-900/20', 'text' => 'text-yellow-800 dark:text-yellow-200', 'dot' => 'bg-yellow-500'],
                                        'partially_paid' => ['bg' => 'bg-blue-100 dark:bg-blue-900/20', 'text' => 'text-blue-800 dark:text-blue-200', 'dot' => 'bg-blue-500'],
                                        'draft' => ['bg' => 'bg-gray-100 dark:bg-gray-900/20', 'text' => 'text-gray-800 dark:text-gray-200', 'dot' => 'bg-gray-500']
                                    ];
                                    $colors = $statusColors[strtolower($status)] ?? $statusColors['draft'];
                                @endphp
                                <div class="flex items-center justify-between p-3 {{ $colors['bg'] }} rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 {{ $colors['dot'] }} rounded-full"></div>
                                        <span class="font-medium {{ $colors['text'] }}">
                                            {{ ucfirst(str_replace('_', ' ', $status)) }}
                                        </span>
                                    </div>
                                    <span class="font-bold {{ $colors['text'] }}">
                                        {{ getCurrencyAmount($amount, true) }}
                                    </span>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                            <p>No outstanding invoices to display</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Action Buttons -->
            @if($hasData)
                <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        Last updated: {{ now()->format('M d, Y H:i') }}
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="refreshOutstandingChart()" class="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Refresh
                        </button>
                        <a href="{{ route('filament.admin.resources.reports.outstanding') }}" class="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm">
                            View Details
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </x-filament::section>

    @if($hasData)
        @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                initializeOutstandingChart();
            });

            let outstandingChart;

            function initializeOutstandingChart() {
                const ctx = document.getElementById('outstandingChart');
                if (!ctx) return;

                const chartConfig = {!! $chartConfig !!};
                
                // Destroy existing chart if it exists
                if (outstandingChart) {
                    outstandingChart.destroy();
                }
                
                try {
                    outstandingChart = new Chart(ctx, chartConfig);
                } catch (error) {
                    console.error('Error initializing outstanding chart:', error);
                    showOutstandingChartError();
                }
            }

            function refreshOutstandingChart() {
                window.location.reload();
            }

            function showOutstandingChartError() {
                const canvas = document.getElementById('outstandingChart');
                if (canvas) {
                    const parent = canvas.parentElement;
                    parent.innerHTML = `
                        <div class="flex items-center justify-center h-64 text-red-500">
                            <div class="text-center">
                                <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                <h3 class="text-lg font-medium mb-2">Chart Error</h3>
                                <p>Unable to load the outstanding invoices chart.</p>
                            </div>
                        </div>
                    `;
                }
            }

            // Handle responsive chart resizing
            window.addEventListener('resize', function() {
                if (outstandingChart) {
                    outstandingChart.resize();
                }
            });
        </script>
        @endpush
    @endif
</x-filament-widgets::widget>
