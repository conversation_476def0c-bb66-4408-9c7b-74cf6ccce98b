@extends('install.layouts.master')

@section('title', 'Server Requirements')

@section('content')
<div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-server text-2xl text-white"></i>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-white">Server Requirements</h1>
                        <p class="text-blue-100 mt-1">Checking your server compatibility</p>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="px-8 py-6">
                <!-- Overall Status -->
                <div class="mb-8">
                    @if($allRequirementsMet)
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 text-xl mr-3"></i>
                                <div>
                                    <h3 class="text-lg font-medium text-green-800">All Requirements Met!</h3>
                                    <p class="text-green-700">Your server meets all the requirements to run Laravel Invoice System.</p>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle text-red-500 text-xl mr-3"></i>
                                <div>
                                    <h3 class="text-lg font-medium text-red-800">Requirements Not Met</h3>
                                    <p class="text-red-700">Please resolve the issues below before continuing with the installation.</p>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- PHP Version -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">PHP Version</h2>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fab fa-php text-2xl text-blue-600 mr-3"></i>
                                <div>
                                    <span class="font-medium">PHP Version (>= 8.1 required)</span>
                                    <div class="text-sm text-gray-600">Current: {{ $phpVersion['full'] }}</div>
                                </div>
                            </div>
                            <div class="flex items-center">
                                @if($phpVersion['supported'])
                                    <i class="fas fa-check-circle text-green-500 text-xl mr-2"></i>
                                    <span class="text-green-600 font-medium">{{ $phpVersion['current'] }}</span>
                                @else
                                    <i class="fas fa-times-circle text-red-500 text-xl mr-2"></i>
                                    <span class="text-red-600 font-medium">{{ $phpVersion['current'] }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- PHP Extensions -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">PHP Extensions</h2>
                    <div class="grid grid-cols-1 gap-4">
                        @foreach ($phpExtensions as $extension => $details)
                            <div class="bg-gray-50 rounded-lg p-4 {{ !$details['required'] ? 'border-l-4 border-blue-300' : ($details['loaded'] ? 'border-l-4 border-green-500' : 'border-l-4 border-red-500') }}">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="fas fa-puzzle-piece {{ $details['required'] ? 'text-gray-600' : 'text-blue-500' }} mr-3"></i>
                                        <div>
                                            <div class="flex items-center">
                                                <span class="font-medium">{{ $extension }}</span>
                                                @if(!$details['required'])
                                                    <span class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">Optional</span>
                                                @endif
                                            </div>
                                            <div class="text-sm text-gray-600">{{ $details['description'] }}</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        @if($details['loaded'])
                                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                            <span class="text-green-600 font-medium">Enabled</span>
                                        @else
                                            @if($details['required'])
                                                <i class="fas fa-times-circle text-red-500 mr-2"></i>
                                                <span class="text-red-600 font-medium">Missing</span>
                                            @else
                                                <i class="fas fa-exclamation-circle text-yellow-500 mr-2"></i>
                                                <span class="text-yellow-600 font-medium">Not Available</span>
                                            @endif
                                        @endif
                                    </div>
                                </div>
                                @if(!$details['loaded'] && $details['required'])
                                    <div class="mt-2 p-2 bg-red-50 rounded text-sm text-red-700">
                                        <strong>Action Required:</strong> This extension is required for the application to work properly.
                                        @if($extension === 'pdo_mysql')
                                            <br>For XAMPP: Uncomment <code>extension=pdo_mysql</code> in php.ini and restart Apache.
                                        @endif
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Server Configuration -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Server Configuration</h2>
                    <div class="space-y-4">
                        @foreach($serverChecks as $check => $details)
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="fas fa-cog text-gray-600 mr-3"></i>
                                        <div>
                                            <span class="font-medium">{{ ucfirst(str_replace('_', ' ', $check)) }}</span>
                                            @if(isset($details['current']))
                                                <div class="text-sm text-gray-600">
                                                    Current: {{ $details['current'] }}
                                                    @if(isset($details['recommended']))
                                                        | Recommended: {{ $details['recommended'] }}
                                                    @endif
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        @if($details['status'])
                                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                            <span class="text-green-600 font-medium">
                                                {{ $details['message'] ?? 'OK' }}
                                            </span>
                                        @else
                                            <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                                            <span class="text-yellow-600 font-medium">
                                                {{ $details['message'] ?? 'Warning' }}
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- 🔥 BEAST MODE TROUBLESHOOTING -->
                @if(!$allRequirementsMet)
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">🔥 BEAST MODE Troubleshooting</h2>

                        <!-- Missing Extensions Fix -->
                        @php
                            $missingCritical = collect($phpExtensions)->filter(function($details, $ext) {
                                return $details['required'] && !$details['loaded'];
                            });
                        @endphp

                        @if($missingCritical->count() > 0)
                            <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
                                <div class="flex items-start">
                                    <i class="fas fa-exclamation-triangle text-red-500 text-xl mr-3 mt-1"></i>
                                    <div class="flex-1">
                                        <h3 class="font-bold text-red-800 text-lg mb-3">🚨 CRITICAL: Missing Required Extensions</h3>

                                        @foreach($missingCritical as $extension => $details)
                                            <div class="mb-6 last:mb-0">
                                                <h4 class="font-semibold text-red-700 mb-2">Missing: {{ $extension }}</h4>

                                                <!-- XAMPP Instructions -->
                                                <div class="mb-4">
                                                    <h5 class="font-medium text-red-600 mb-2">🔧 For XAMPP Users:</h5>
                                                    <div class="bg-gray-800 text-green-400 p-3 rounded text-sm font-mono">
                                                        <div class="mb-2">1. Open XAMPP Control Panel</div>
                                                        <div class="mb-2">2. Click "Config" next to Apache → "PHP (php.ini)"</div>
                                                        <div class="mb-2">3. Find: <span class="text-yellow-300">;extension={{ $extension }}</span></div>
                                                        <div class="mb-2">4. Remove semicolon: <span class="text-green-300">extension={{ $extension }}</span></div>
                                                        <div class="mb-2">5. Save file and restart Apache</div>
                                                        <div>6. Refresh this page</div>
                                                    </div>
                                                </div>

                                                <!-- Linux Instructions -->
                                                <div class="mb-4">
                                                    <h5 class="font-medium text-red-600 mb-2">🐧 For Linux Users:</h5>
                                                    <div class="bg-gray-800 text-green-400 p-3 rounded text-sm font-mono">
                                                        <div class="mb-1">Ubuntu/Debian: <span class="text-yellow-300">sudo apt-get install php-mysql</span></div>
                                                        <div class="mb-1">CentOS/RHEL: <span class="text-yellow-300">sudo yum install php-mysql</span></div>
                                                        <div>Then: <span class="text-yellow-300">sudo systemctl restart apache2</span></div>
                                                    </div>
                                                </div>

                                                <!-- Windows Instructions -->
                                                <div>
                                                    <h5 class="font-medium text-red-600 mb-2">🪟 For Windows (WAMP/Manual):</h5>
                                                    <div class="bg-gray-800 text-green-400 p-3 rounded text-sm font-mono">
                                                        <div class="mb-1">1. Locate php.ini file</div>
                                                        <div class="mb-1">2. Uncomment: extension={{ $extension }}</div>
                                                        <div>3. Restart web server</div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach

                                        <div class="mt-4 p-3 bg-blue-100 rounded">
                                            <p class="text-blue-800 font-medium">
                                                ⚡ After making changes, restart your web server and refresh this page!
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- General Troubleshooting -->
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <h3 class="font-medium text-yellow-800 mb-2">🛠️ General Solutions:</h3>
                            <ul class="text-sm text-yellow-700 space-y-1">
                                <li>• <strong>Shared Hosting:</strong> Contact your hosting provider to enable missing extensions</li>
                                <li>• <strong>VPS/Dedicated:</strong> Install extensions using package manager</li>
                                <li>• <strong>Local Development:</strong> Enable extensions in php.ini and restart server</li>
                                <li>• <strong>Docker:</strong> Use a PHP image with required extensions pre-installed</li>
                            </ul>
                        </div>

                        <!-- Quick Test Button -->
                        <div class="mt-4 text-center">
                            <button onclick="window.location.reload()"
                                    class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                <i class="fas fa-sync-alt mr-2"></i>
                                🔥 RECHECK REQUIREMENTS (BEAST MODE)
                            </button>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Footer -->
            <div class="bg-gray-50 px-8 py-4">
                <div class="flex items-center justify-between">
                    <a href="{{ route('install.index') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back
                    </a>

                    @if($allRequirementsMet)
                        <a href="{{ route('install.folder-permissions') }}"
                           class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Next: Check Permissions
                            <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    @else
                        <button type="button"
                                onclick="window.location.reload()"
                                class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-400 hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                            <i class="fas fa-sync-alt mr-2"></i>
                            Recheck Requirements
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection