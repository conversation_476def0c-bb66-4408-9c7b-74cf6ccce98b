<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Config;
use Symfony\Component\HttpFoundation\Response;

class InstallationSession
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 🔥 BEAST MODE: Force file-based sessions during installation
        if (!file_exists(storage_path('installed'))) {
            // CRITICAL: Use file driver to avoid database dependency
            Config::set('session.driver', 'file');

            // Extend session lifetime during installation (24 hours)
            Config::set('session.lifetime', 1440);
            Config::set('session.expire_on_close', false);

            // Ensure session directory exists and is writable
            $sessionPath = storage_path('framework/sessions');
            if (!file_exists($sessionPath)) {
                mkdir($sessionPath, 0755, true);
            }

            // Set session configuration
            Config::set('session.files', $sessionPath);
            Config::set('session.encrypt', false); // Disable encryption during install

            // BEAST MODE: Ensure session cookie settings are installation-friendly
            Config::set('session.cookie', 'laravel_install_session');
            Config::set('session.path', '/');
            Config::set('session.domain', null);
            Config::set('session.secure', false); // Allow HTTP during installation
            Config::set('session.http_only', true);
            Config::set('session.same_site', 'lax');

            // Force session to start if not already started
            if (!$request->hasSession()) {
                $request->setLaravelSession(app('session.store'));
            }

            // Regenerate session ID to prevent fixation
            if (!$request->session()->has('installation_started')) {
                $request->session()->regenerate();
                $request->session()->put('installation_started', true);
                $request->session()->put('installation_start_time', now());
            }
        }

        $response = $next($request);

        // Add installation progress to session
        if ($request->route() && strpos($request->route()->getName(), 'install.') === 0) {
            $this->trackInstallationProgress($request);
        }

        return $response;
    }

    /**
     * Track installation progress in session
     */
    private function trackInstallationProgress(Request $request)
    {
        $routeName = $request->route()->getName();
        $steps = [
            'install.index' => 0,
            'install.server-requirements' => 1,
            'install.folder-permissions' => 2,
            'install.database' => 3,
            'install.mail' => 4,
            'install.admin' => 5,
            'install.branding' => 6,
            'install.summary' => 7,
            'install.success' => 8,
        ];

        if (isset($steps[$routeName])) {
            Session::put('installation_step', $steps[$routeName]);
            Session::put('installation_last_activity', now());
        }

        // Store completed steps
        $completedSteps = Session::get('installation_completed_steps', []);
        if ($request->isMethod('POST') && isset($steps[$routeName])) {
            $completedSteps[] = $steps[$routeName];
            Session::put('installation_completed_steps', array_unique($completedSteps));
        }
    }
}
