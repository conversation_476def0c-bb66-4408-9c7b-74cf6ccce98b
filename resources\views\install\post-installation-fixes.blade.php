<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 BEAST MODE: Post-Installation Fixes</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-12 px-4">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                <!-- Header -->
                <div class="bg-gradient-to-r from-green-600 to-green-700 px-8 py-6">
                    <h1 class="text-2xl font-bold text-white">🔥 BEAST MODE: Post-Installation Fixes</h1>
                    <p class="text-green-100 mt-1">Fix login and account creation issues</p>
                </div>

                <!-- Content -->
                <div class="px-8 py-6" x-data="postInstallationFixes()">
                    <!-- Status Display -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">📊 Current Status</h2>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div id="status-display" class="text-sm">
                                <div class="text-blue-600">🔄 Loading status...</div>
                            </div>
                        </div>
                    </div>

                    <!-- Fix Actions -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">🔧 Fix Actions</h2>
                        <div class="space-y-4">
                            <!-- Fix User Table -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h3 class="font-medium mb-2">1. Fix User Table for Registration</h3>
                                <p class="text-sm text-gray-600 mb-3">Add name column and update existing users for Filament compatibility</p>
                                <button @click="fixUserTable()" 
                                        :disabled="loading"
                                        class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50">
                                    <i class="fas fa-database mr-2"></i>
                                    Fix User Table
                                </button>
                                <div id="fix-user-table-result" class="mt-2 text-sm"></div>
                            </div>

                            <!-- Check Auth Diagnostics -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h3 class="font-medium mb-2">2. Check Authentication Setup</h3>
                                <p class="text-sm text-gray-600 mb-3">Verify users, roles, and authentication configuration</p>
                                <button @click="checkAuthDiagnostics()" 
                                        :disabled="loading"
                                        class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50">
                                    <i class="fas fa-user-check mr-2"></i>
                                    Check Auth Setup
                                </button>
                                <div id="auth-diagnostics-result" class="mt-2 text-sm"></div>
                            </div>

                            <!-- Test Login -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h3 class="font-medium mb-2">3. Test Login Flow</h3>
                                <p class="text-sm text-gray-600 mb-3">Test the login process with your admin credentials</p>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                    <input type="email" x-model="testEmail" placeholder="Admin Email" 
                                           class="border rounded px-3 py-2 text-sm">
                                    <input type="password" x-model="testPassword" placeholder="Admin Password" 
                                           class="border rounded px-3 py-2 text-sm">
                                </div>
                                <button @click="testLogin()" 
                                        :disabled="loading || !testEmail || !testPassword"
                                        class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:opacity-50">
                                    <i class="fas fa-sign-in-alt mr-2"></i>
                                    Test Login
                                </button>
                                <div id="test-login-result" class="mt-2 text-sm"></div>
                            </div>

                            <!-- Fix User Roles -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h3 class="font-medium mb-2">4. Fix User Roles</h3>
                                <p class="text-sm text-gray-600 mb-3">Ensure all users have proper roles assigned</p>
                                <button @click="fixUserRoles()" 
                                        :disabled="loading"
                                        class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 disabled:opacity-50">
                                    <i class="fas fa-user-cog mr-2"></i>
                                    Fix User Roles
                                </button>
                                <div id="fix-roles-result" class="mt-2 text-sm"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">🔗 Quick Links</h2>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <a href="/admin/login" target="_blank"
                               class="bg-blue-100 border border-blue-300 rounded-lg p-4 text-center hover:bg-blue-200">
                                <i class="fas fa-sign-in-alt text-blue-600 mb-2"></i>
                                <div class="font-medium text-blue-800">Admin Login</div>
                            </a>
                            
                            <a href="/admin/register" target="_blank"
                               class="bg-green-100 border border-green-300 rounded-lg p-4 text-center hover:bg-green-200">
                                <i class="fas fa-user-plus text-green-600 mb-2"></i>
                                <div class="font-medium text-green-800">Admin Register</div>
                            </a>
                            
                            <a href="/auth-diagnostics" target="_blank"
                               class="bg-purple-100 border border-purple-300 rounded-lg p-4 text-center hover:bg-purple-200">
                                <i class="fas fa-chart-line text-purple-600 mb-2"></i>
                                <div class="font-medium text-purple-800">Auth Diagnostics</div>
                            </a>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <div class="flex items-center justify-between pt-6 border-t">
                        <a href="/install-diagnostics" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Installation Diagnostics
                        </a>
                        
                        <a href="/admin" 
                           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                            Go to Admin Panel
                            <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        function postInstallationFixes() {
            return {
                loading: false,
                testEmail: '',
                testPassword: '',
                
                init() {
                    this.checkAuthDiagnostics();
                },
                
                async fixUserTable() {
                    this.loading = true;
                    const resultDiv = document.getElementById('fix-user-table-result');
                    resultDiv.innerHTML = '<span class="text-blue-600">🔄 Fixing user table...</span>';
                    
                    try {
                        const response = await fetch('/fix-user-table');
                        const data = await response.json();
                        
                        if (data.success) {
                            resultDiv.innerHTML = `<span class="text-green-600">✅ ${data.results.join('<br>✅ ')}</span>`;
                        } else {
                            resultDiv.innerHTML = `<span class="text-red-600">❌ Error: ${data.error}</span>`;
                        }
                    } catch (error) {
                        resultDiv.innerHTML = `<span class="text-red-600">❌ Error: ${error.message}</span>`;
                    } finally {
                        this.loading = false;
                    }
                },
                
                async checkAuthDiagnostics() {
                    const statusDiv = document.getElementById('status-display');
                    const resultDiv = document.getElementById('auth-diagnostics-result');
                    
                    try {
                        const response = await fetch('/auth-diagnostics');
                        const data = await response.json();
                        
                        let statusHtml = `
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div><strong>Users:</strong> ${data.users_count}</div>
                                <div><strong>Roles:</strong> ${data.roles_count}</div>
                            </div>
                            <div class="mt-4">
                                <strong>Users:</strong>
                                <ul class="list-disc list-inside mt-2">
                        `;
                        
                        data.users.forEach(user => {
                            statusHtml += `<li>${user.email} - Roles: ${user.roles.join(', ') || 'None'}</li>`;
                        });
                        
                        statusHtml += '</ul></div>';
                        statusDiv.innerHTML = statusHtml;
                        
                        if (resultDiv) {
                            resultDiv.innerHTML = '<span class="text-green-600">✅ Auth diagnostics loaded</span>';
                        }
                        
                    } catch (error) {
                        statusDiv.innerHTML = `<span class="text-red-600">❌ Error loading status: ${error.message}</span>`;
                        if (resultDiv) {
                            resultDiv.innerHTML = `<span class="text-red-600">❌ Error: ${error.message}</span>`;
                        }
                    }
                },
                
                async testLogin() {
                    this.loading = true;
                    const resultDiv = document.getElementById('test-login-result');
                    resultDiv.innerHTML = '<span class="text-blue-600">🔄 Testing login...</span>';
                    
                    // This is a simplified test - in reality, you'd need to implement proper login testing
                    resultDiv.innerHTML = '<span class="text-blue-600">ℹ️  Please test login manually using the Admin Login link above</span>';
                    this.loading = false;
                },
                
                async fixUserRoles() {
                    this.loading = true;
                    const resultDiv = document.getElementById('fix-roles-result');
                    resultDiv.innerHTML = '<span class="text-blue-600">🔄 Fixing user roles...</span>';
                    
                    // This would need a backend endpoint to fix roles
                    resultDiv.innerHTML = '<span class="text-blue-600">ℹ️  Role fixing functionality would be implemented here</span>';
                    this.loading = false;
                }
            }
        }
    </script>
</body>
</html>
