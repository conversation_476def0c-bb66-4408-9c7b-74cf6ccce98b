<?php
/**
 * 🔥 BEAST MODE: Complete Installation Flow Test Script
 * This script tests the entire installation process to ensure everything works
 */

echo "🔥 BEAST MODE: Installation Flow Test Script\n";
echo "============================================\n\n";

// Check if we're in a Laravel project
if (!file_exists('artisan')) {
    echo "❌ Error: Not in a Laravel project directory\n";
    echo "Please run this script from your Laravel project root.\n";
    exit(1);
}

echo "🧪 Testing Complete Installation Flow...\n\n";

// Load Laravel
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

$testResults = [];
$overallSuccess = true;

// Test 1: Environment Configuration
echo "1️⃣  Testing Environment Configuration...\n";
try {
    // Check .env file
    if (!file_exists('.env')) {
        throw new Exception('.env file not found');
    }
    
    // Check APP_KEY
    $appKey = config('app.key');
    if (empty($appKey)) {
        throw new Exception('APP_KEY not set');
    }
    
    // Check database configuration
    $dbHost = config('database.connections.mysql.host');
    $dbName = config('database.connections.mysql.database');
    $dbUser = config('database.connections.mysql.username');
    
    if (empty($dbHost) || empty($dbName) || empty($dbUser)) {
        throw new Exception('Database configuration incomplete');
    }
    
    echo "   ✅ Environment configuration is valid\n";
    $testResults['environment'] = true;
    
} catch (Exception $e) {
    echo "   ❌ Environment configuration failed: " . $e->getMessage() . "\n";
    $testResults['environment'] = false;
    $overallSuccess = false;
}

// Test 2: Database Connection
echo "\n2️⃣  Testing Database Connection...\n";
try {
    $pdo = DB::connection()->getPdo();
    $databaseName = DB::connection()->getDatabaseName();
    echo "   ✅ Database connection successful (Database: {$databaseName})\n";
    $testResults['database_connection'] = true;
    
} catch (Exception $e) {
    echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
    $testResults['database_connection'] = false;
    $overallSuccess = false;
}

// Test 3: Cache Configuration
echo "\n3️⃣  Testing Cache Configuration...\n";
try {
    // Test file cache (should work during installation)
    Config::set('cache.default', 'file');
    Cache::put('test_key', 'test_value', 60);
    $value = Cache::get('test_key');
    
    if ($value !== 'test_value') {
        throw new Exception('Cache test failed');
    }
    
    Cache::forget('test_key');
    echo "   ✅ File cache is working correctly\n";
    $testResults['cache'] = true;
    
} catch (Exception $e) {
    echo "   ❌ Cache test failed: " . $e->getMessage() . "\n";
    $testResults['cache'] = false;
    $overallSuccess = false;
}

// Test 4: Migration System
echo "\n4️⃣  Testing Migration System...\n";
try {
    // Check if migrations table exists
    if (!Schema::hasTable('migrations')) {
        echo "   ℹ️  Migrations table doesn't exist, creating...\n";
        Artisan::call('migrate:install');
    }
    
    // Get migration status
    $output = [];
    exec('php artisan migrate:status 2>&1', $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "   ✅ Migration system is functional\n";
        $testResults['migrations'] = true;
    } else {
        throw new Exception('Migration status check failed');
    }
    
} catch (Exception $e) {
    echo "   ❌ Migration system test failed: " . $e->getMessage() . "\n";
    $testResults['migrations'] = false;
    $overallSuccess = false;
}

// Test 5: Critical Tables Check
echo "\n5️⃣  Testing Critical Tables...\n";
try {
    $criticalTables = ['users', 'roles', 'permissions', 'cache', 'sessions'];
    $existingTables = [];
    $missingTables = [];
    
    foreach ($criticalTables as $table) {
        if (Schema::hasTable($table)) {
            $existingTables[] = $table;
            echo "   ✅ Table exists: {$table}\n";
        } else {
            $missingTables[] = $table;
            echo "   ⚠️  Table missing: {$table}\n";
        }
    }
    
    if (empty($missingTables)) {
        echo "   ✅ All critical tables exist\n";
        $testResults['tables'] = true;
    } else {
        echo "   ℹ️  Missing tables will be created during installation\n";
        $testResults['tables'] = 'partial';
    }
    
} catch (Exception $e) {
    echo "   ❌ Table check failed: " . $e->getMessage() . "\n";
    $testResults['tables'] = false;
    $overallSuccess = false;
}

// Test 6: Seeder Data Check
echo "\n6️⃣  Testing Seeder Data...\n";
try {
    if (Schema::hasTable('roles')) {
        $roleCount = DB::table('roles')->count();
        if ($roleCount > 0) {
            echo "   ✅ Roles data exists ({$roleCount} roles)\n";
            $testResults['seeders'] = true;
        } else {
            echo "   ℹ️  No roles data found, will be seeded during installation\n";
            $testResults['seeders'] = 'partial';
        }
    } else {
        echo "   ℹ️  Roles table doesn't exist, will be created during installation\n";
        $testResults['seeders'] = 'partial';
    }
    
} catch (Exception $e) {
    echo "   ❌ Seeder data check failed: " . $e->getMessage() . "\n";
    $testResults['seeders'] = false;
    $overallSuccess = false;
}

// Test 7: File Permissions
echo "\n7️⃣  Testing File Permissions...\n";
try {
    $directories = [
        'storage',
        'storage/framework',
        'storage/framework/sessions',
        'storage/framework/views',
        'storage/framework/cache',
        'storage/logs',
        'bootstrap/cache'
    ];
    
    $permissionIssues = [];
    
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
            echo "   ✅ Created directory: {$dir}\n";
        }
        
        if (!is_writable($dir)) {
            $permissionIssues[] = $dir;
            echo "   ⚠️  Directory not writable: {$dir}\n";
        } else {
            echo "   ✅ Directory writable: {$dir}\n";
        }
    }
    
    if (empty($permissionIssues)) {
        echo "   ✅ All directories have correct permissions\n";
        $testResults['permissions'] = true;
    } else {
        echo "   ⚠️  Some directories need permission fixes\n";
        $testResults['permissions'] = 'partial';
    }
    
} catch (Exception $e) {
    echo "   ❌ Permission check failed: " . $e->getMessage() . "\n";
    $testResults['permissions'] = false;
    $overallSuccess = false;
}

// Test 8: Installation Lock Check
echo "\n8️⃣  Testing Installation Lock...\n";
try {
    $lockFile = storage_path('installed');
    
    if (file_exists($lockFile)) {
        echo "   ℹ️  Installation lock file exists\n";
        echo "   ⚠️  Installation wizard may be locked\n";
        $testResults['lock'] = 'locked';
    } else {
        echo "   ✅ No installation lock found\n";
        echo "   ✅ Installation wizard should be accessible\n";
        $testResults['lock'] = true;
    }
    
} catch (Exception $e) {
    echo "   ❌ Lock check failed: " . $e->getMessage() . "\n";
    $testResults['lock'] = false;
    $overallSuccess = false;
}

// Summary
echo "\n📊 TEST SUMMARY:\n";
echo "================\n";

foreach ($testResults as $test => $result) {
    $status = '';
    $icon = '';
    
    if ($result === true) {
        $status = 'PASSED';
        $icon = '✅';
    } elseif ($result === 'partial') {
        $status = 'PARTIAL';
        $icon = '⚠️ ';
    } elseif ($result === 'locked') {
        $status = 'LOCKED';
        $icon = '🔒';
    } else {
        $status = 'FAILED';
        $icon = '❌';
    }
    
    echo sprintf("%-20s %s %s\n", ucwords(str_replace('_', ' ', $test)) . ':', $icon, $status);
}

echo "\n";

if ($overallSuccess) {
    echo "🎉 OVERALL STATUS: READY FOR INSTALLATION\n";
    echo "Your system is ready for the Laravel Invoice Management System installation.\n\n";
    
    echo "🚀 Next Steps:\n";
    echo "1. Visit your installation URL: http://your-domain.com/install\n";
    echo "2. Follow the installation wizard\n";
    echo "3. Complete all steps including admin account creation\n";
} else {
    echo "⚠️  OVERALL STATUS: ISSUES DETECTED\n";
    echo "Some issues were detected that may affect installation.\n\n";
    
    echo "🔧 Recommended Actions:\n";
    echo "1. Fix the failed tests above\n";
    echo "2. Run the requirements checker: php check-requirements.php\n";
    echo "3. Run the setup script: ./install-server-setup.sh\n";
    echo "4. Re-run this test script\n";
}

echo "\n🔥 Installation Flow Test Complete!\n";
