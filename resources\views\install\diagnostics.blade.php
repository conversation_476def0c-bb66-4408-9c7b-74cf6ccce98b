<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 BEAST MODE: Installation Diagnostics</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-12 px-4">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                <!-- Header -->
                <div class="bg-gradient-to-r from-red-600 to-red-700 px-8 py-6">
                    <h1 class="text-2xl font-bold text-white">🔥 BEAST MODE: Installation Diagnostics</h1>
                    <p class="text-red-100 mt-1">Debug and test installation components</p>
                </div>

                <!-- Content -->
                <div class="px-8 py-6">
                    <!-- System Information -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">📊 System Information</h2>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <strong>PHP Version:</strong> {{ phpversion() }}
                                </div>
                                <div>
                                    <strong>Laravel Version:</strong> {{ app()->version() }}
                                </div>
                                <div>
                                    <strong>Environment:</strong> {{ config('app.env') }}
                                </div>
                                <div>
                                    <strong>Debug Mode:</strong> {{ config('app.debug') ? 'Enabled' : 'Disabled' }}
                                </div>
                                <div>
                                    <strong>Cache Driver:</strong> {{ config('cache.default') }}
                                </div>
                                <div>
                                    <strong>Session Driver:</strong> {{ config('session.driver') }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Routes -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">🧪 Test Routes</h2>
                        <div class="space-y-4">
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h3 class="font-medium mb-2">Basic JSON Test</h3>
                                <button onclick="testRoute('/test-web-migration')" 
                                        class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                                    Test Basic JSON Response
                                </button>
                                <div id="test-basic" class="mt-2 text-sm"></div>
                            </div>

                            <div class="bg-gray-50 rounded-lg p-4">
                                <h3 class="font-medium mb-2">Controller Test</h3>
                                <button onclick="testRoute('/test-web-migration-controller')" 
                                        class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                                    Test WebMigrationController
                                </button>
                                <div id="test-controller" class="mt-2 text-sm"></div>
                            </div>

                            <div class="bg-gray-50 rounded-lg p-4">
                                <h3 class="font-medium mb-2">Status Test</h3>
                                <button onclick="testRoute('/test-web-migration-status')" 
                                        class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                                    Test Status Endpoint
                                </button>
                                <div id="test-status" class="mt-2 text-sm"></div>
                            </div>

                            <div class="bg-gray-50 rounded-lg p-4">
                                <h3 class="font-medium mb-2">Installation Routes Test</h3>
                                <button onclick="testRoute('/install/web-migration/test')" 
                                        class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">
                                    Test Installation Route
                                </button>
                                <div id="test-install" class="mt-2 text-sm"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Database Test -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">🗄️ Database Test</h2>
                        <div class="bg-gray-50 rounded-lg p-4">
                            @try
                                @php
                                    $pdo = DB::connection()->getPdo();
                                    $dbName = DB::connection()->getDatabaseName();
                                @endphp
                                <div class="text-green-600">
                                    ✅ Database connection successful<br>
                                    <strong>Database:</strong> {{ $dbName }}
                                </div>
                            @catch(Exception $e)
                                <div class="text-red-600">
                                    ❌ Database connection failed: {{ $e->getMessage() }}
                                </div>
                            @endtry
                        </div>
                    </div>

                    <!-- File Permissions -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">📁 File Permissions</h2>
                        <div class="bg-gray-50 rounded-lg p-4">
                            @php
                                $directories = [
                                    'storage' => storage_path(),
                                    'storage/framework' => storage_path('framework'),
                                    'storage/logs' => storage_path('logs'),
                                    'bootstrap/cache' => base_path('bootstrap/cache')
                                ];
                            @endphp
                            
                            @foreach($directories as $name => $path)
                                <div class="mb-2">
                                    @if(is_writable($path))
                                        <span class="text-green-600">✅ {{ $name }}: Writable</span>
                                    @else
                                        <span class="text-red-600">❌ {{ $name }}: Not writable</span>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Error Logs -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">📋 Recent Error Logs</h2>
                        <div class="bg-gray-50 rounded-lg p-4">
                            @php
                                $logFile = storage_path('logs/laravel.log');
                                $logs = [];
                                if (file_exists($logFile)) {
                                    $content = file_get_contents($logFile);
                                    $lines = explode("\n", $content);
                                    $logs = array_slice(array_reverse($lines), 0, 10);
                                }
                            @endphp
                            
                            @if(empty($logs))
                                <div class="text-gray-600">No recent error logs found</div>
                            @else
                                <div class="text-xs font-mono bg-gray-800 text-green-400 p-3 rounded max-h-64 overflow-y-auto">
                                    @foreach($logs as $log)
                                        @if(trim($log))
                                            <div class="mb-1">{{ $log }}</div>
                                        @endif
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Navigation -->
                    <div class="flex items-center justify-between pt-6 border-t">
                        <a href="/install" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Installation
                        </a>
                        
                        <a href="/install/web-migration" 
                           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            Web Migration Tool
                            <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function testRoute(url) {
            const resultId = 'test-' + url.split('/').pop().replace('-', '');
            const resultDiv = document.getElementById(resultId) || document.getElementById('test-basic');
            
            resultDiv.innerHTML = '<span class="text-blue-600">🔄 Testing...</span>';
            
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                const contentType = response.headers.get('content-type');
                
                if (contentType && contentType.includes('application/json')) {
                    const data = await response.json();
                    resultDiv.innerHTML = `<span class="text-green-600">✅ JSON Response: ${JSON.stringify(data, null, 2)}</span>`;
                } else {
                    const text = await response.text();
                    resultDiv.innerHTML = `<span class="text-red-600">❌ HTML Response (${response.status}): ${text.substring(0, 200)}...</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="text-red-600">❌ Error: ${error.message}</span>`;
            }
        }
    </script>
</body>
</html>
