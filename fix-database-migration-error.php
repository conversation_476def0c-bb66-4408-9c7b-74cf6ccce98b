<?php
/**
 * 🔥 BEAST MODE: Database Migration Error Fix Script
 * Run this script if you encounter database migration issues during installation
 */

echo "🔥 BEAST MODE: Database Migration Error Fix Script\n";
echo "=================================================\n\n";

// Check if we're in a Laravel project
if (!file_exists('artisan')) {
    echo "❌ Error: Not in a Laravel project directory\n";
    echo "Please run this script from your Laravel project root.\n";
    exit(1);
}

echo "🔧 Fixing Database Migration Errors...\n\n";

// Step 1: Switch to file cache
echo "1️⃣  Switching to file cache driver...\n";
$envFile = '.env';
if (file_exists($envFile)) {
    $envContent = file_get_contents($envFile);
    
    // Update or add CACHE_STORE
    if (strpos($envContent, 'CACHE_STORE=') !== false) {
        $envContent = preg_replace('/CACHE_STORE=.*/', 'CACHE_STORE=file', $envContent);
    } else {
        $envContent .= "\nCACHE_STORE=file\n";
    }
    
    file_put_contents($envFile, $envContent);
    echo "   ✅ Cache driver set to file\n";
} else {
    echo "   ⚠️  .env file not found\n";
}

// Step 2: Clear all caches
echo "\n2️⃣  Clearing all caches...\n";
$commands = [
    'php artisan config:clear',
    'php artisan cache:clear',
    'php artisan view:clear',
    'php artisan route:clear'
];

foreach ($commands as $command) {
    echo "   Running: {$command}\n";
    $output = [];
    $returnCode = 0;
    exec($command . ' 2>&1', $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "   ✅ Success\n";
    } else {
        echo "   ⚠️  Warning: " . implode("\n", $output) . "\n";
    }
}

// Step 3: Test database connection
echo "\n3️⃣  Testing database connection...\n";
try {
    // Load Laravel
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    // Test database connection
    $pdo = DB::connection()->getPdo();
    echo "   ✅ Database connection successful\n";
    
    // Get database name
    $databaseName = DB::connection()->getDatabaseName();
    echo "   ℹ️  Connected to database: {$databaseName}\n";
    
} catch (Exception $e) {
    echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
    echo "   🔧 Please check your database configuration in .env file\n";
    exit(1);
}

// Step 4: Check existing tables
echo "\n4️⃣  Checking existing tables...\n";
try {
    $tables = DB::select('SHOW TABLES');
    $tableCount = count($tables);
    echo "   ℹ️  Found {$tableCount} existing tables\n";
    
    // Check for critical tables
    $criticalTables = ['users', 'roles', 'permissions', 'cache', 'sessions', 'migrations'];
    $existingCriticalTables = [];
    
    foreach ($tables as $table) {
        $tableName = array_values((array)$table)[0];
        if (in_array($tableName, $criticalTables)) {
            $existingCriticalTables[] = $tableName;
        }
    }
    
    echo "   ✅ Critical tables found: " . implode(', ', $existingCriticalTables) . "\n";
    
    $missingTables = array_diff($criticalTables, $existingCriticalTables);
    if (!empty($missingTables)) {
        echo "   ⚠️  Missing critical tables: " . implode(', ', $missingTables) . "\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Error checking tables: " . $e->getMessage() . "\n";
}

// Step 5: Run migrations
echo "\n5️⃣  Running database migrations...\n";
try {
    $output = [];
    $returnCode = 0;
    exec('php artisan migrate --force 2>&1', $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "   ✅ Migrations completed successfully\n";
        echo "   📋 Migration output:\n";
        foreach ($output as $line) {
            echo "      " . $line . "\n";
        }
    } else {
        echo "   ❌ Migration failed with exit code: {$returnCode}\n";
        echo "   📋 Error output:\n";
        foreach ($output as $line) {
            echo "      " . $line . "\n";
        }
    }
} catch (Exception $e) {
    echo "   ❌ Migration error: " . $e->getMessage() . "\n";
}

// Step 6: Run seeders
echo "\n6️⃣  Running database seeders...\n";
try {
    $output = [];
    $returnCode = 0;
    exec('php artisan db:seed --force 2>&1', $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "   ✅ Seeders completed successfully\n";
        echo "   📋 Seeder output:\n";
        foreach ($output as $line) {
            echo "      " . $line . "\n";
        }
    } else {
        echo "   ❌ Seeding failed with exit code: {$returnCode}\n";
        echo "   📋 Error output:\n";
        foreach ($output as $line) {
            echo "      " . $line . "\n";
        }
    }
} catch (Exception $e) {
    echo "   ❌ Seeding error: " . $e->getMessage() . "\n";
}

// Step 7: Verify final state
echo "\n7️⃣  Verifying final database state...\n";
try {
    // Check critical tables again
    $tables = DB::select('SHOW TABLES');
    $tableNames = array_map(function($table) {
        return array_values((array)$table)[0];
    }, $tables);
    
    $criticalTables = ['users', 'roles', 'permissions', 'cache', 'sessions', 'migrations'];
    $allTablesExist = true;
    
    foreach ($criticalTables as $table) {
        if (in_array($table, $tableNames)) {
            echo "   ✅ Table verified: {$table}\n";
        } else {
            echo "   ❌ Table missing: {$table}\n";
            $allTablesExist = false;
        }
    }
    
    // Check if roles exist
    if (in_array('roles', $tableNames)) {
        $roleCount = DB::table('roles')->count();
        echo "   ℹ️  Roles in database: {$roleCount}\n";
        
        if ($roleCount > 0) {
            echo "   ✅ Roles seeded successfully\n";
        } else {
            echo "   ⚠️  No roles found, seeding may have failed\n";
        }
    }
    
    if ($allTablesExist) {
        echo "\n🎉 SUCCESS!\n";
        echo "All critical tables are now present in the database.\n";
        echo "You can now continue with the installation process.\n\n";
        
        echo "🚀 Next steps:\n";
        echo "1. Visit your installation URL: http://your-domain.com/install\n";
        echo "2. Continue with the admin account creation step\n";
        echo "3. Complete the installation wizard\n";
    } else {
        echo "\n⚠️  PARTIAL SUCCESS\n";
        echo "Some tables are still missing. Please check the error messages above.\n";
        echo "You may need to manually create the missing tables or fix database permissions.\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Verification error: " . $e->getMessage() . "\n";
}

echo "\n🔥 Database Migration Fix Complete!\n";
echo "Check the output above for any remaining issues.\n";
