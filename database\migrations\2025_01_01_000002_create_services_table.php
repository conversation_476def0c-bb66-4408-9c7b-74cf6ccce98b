<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code', 10)->unique();
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->decimal('unit_price', 10, 2);
            $table->string('unit_type', 50);
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->json('pricing_tiers')->nullable();
            $table->timestamps();

            $table->index(['is_active', 'category_id']);
            $table->index('code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};
