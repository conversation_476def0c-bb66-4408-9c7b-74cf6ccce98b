<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\User;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('name')->nullable()->after('id');
        });

        // Populate the name field for existing users
        try {
            $users = User::all();
            foreach ($users as $user) {
                $name = trim($user->first_name . ' ' . $user->last_name);
                if ($name) {
                    $user->update(['name' => $name]);
                }
            }
        } catch (\Exception $e) {
            // If there's an error, continue - this is just for existing data
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('name');
        });
    }
};
