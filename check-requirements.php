<?php
/**
 * 🔥 BEAST MODE: Laravel Invoice Management System - Requirements Checker
 * Run this script to verify your server meets all requirements
 */

echo "🔥 BEAST MODE: Server Requirements Checker\n";
echo "==========================================\n\n";

$errors = [];
$warnings = [];
$success = [];

// Check PHP version
echo "📋 PHP VERSION CHECK:\n";
$phpVersion = phpversion();
$requiredVersion = '8.1.0';

if (version_compare($phpVersion, $requiredVersion, '>=')) {
    $success[] = "PHP Version: {$phpVersion} ✅";
    echo "✅ PHP Version: {$phpVersion} (Required: >= {$requiredVersion})\n";
} else {
    $errors[] = "PHP Version: {$phpVersion} (Required: >= {$requiredVersion})";
    echo "❌ PHP Version: {$phpVersion} (Required: >= {$requiredVersion})\n";
}

echo "\n🧩 PHP EXTENSIONS CHECK:\n";

$requiredExtensions = [
    'pdo' => 'Required for database connections',
    'pdo_mysql' => 'Required for MySQL database connections',
    'mbstring' => 'Required for string manipulation',
    'openssl' => 'Required for encryption and secure connections',
    'tokenizer' => 'Required for PHP tokenization',
    'xml' => 'Required for XML processing',
    'ctype' => 'Required for character type checking',
    'fileinfo' => 'Required for file type detection',
    'bcmath' => 'Required for arbitrary precision mathematics',
    'curl' => 'Required for HTTP requests',
    'gd' => 'Required for image processing',
    'zip' => 'Optional for ZIP file handling',
    'json' => 'Required for JSON processing'
];

$optionalExtensions = ['zip', 'pdo_pgsql'];

foreach ($requiredExtensions as $extension => $description) {
    $loaded = extension_loaded($extension);
    $isOptional = in_array($extension, $optionalExtensions);
    
    if ($loaded) {
        $success[] = "{$extension} extension";
        echo "✅ {$extension}: Loaded ({$description})\n";
    } else {
        if ($isOptional) {
            $warnings[] = "{$extension} extension (optional)";
            echo "⚠️  {$extension}: Not loaded ({$description}) - Optional\n";
        } else {
            $errors[] = "{$extension} extension";
            echo "❌ {$extension}: Not loaded ({$description}) - Required\n";
        }
    }
}

echo "\n📁 DIRECTORY PERMISSIONS CHECK:\n";

$requiredDirectories = [
    'storage' => 'Required for logs, cache, and sessions',
    'storage/framework' => 'Required for framework cache',
    'storage/framework/sessions' => 'Required for session storage',
    'storage/framework/views' => 'Required for compiled views',
    'storage/framework/cache' => 'Required for application cache',
    'storage/logs' => 'Required for log files',
    'bootstrap/cache' => 'Required for configuration cache'
];

foreach ($requiredDirectories as $directory => $description) {
    if (!file_exists($directory)) {
        @mkdir($directory, 0755, true);
    }
    
    if (is_writable($directory)) {
        $success[] = "{$directory} directory permissions";
        echo "✅ {$directory}: Writable ({$description})\n";
    } else {
        $errors[] = "{$directory} directory permissions";
        echo "❌ {$directory}: Not writable ({$description})\n";
    }
}

echo "\n🔧 SERVER CONFIGURATION CHECK:\n";

// Memory limit
$memoryLimit = ini_get('memory_limit');
$memoryLimitBytes = return_bytes($memoryLimit);
$recommendedMemory = return_bytes('256M');

if ($memoryLimitBytes >= $recommendedMemory || $memoryLimit == -1) {
    $success[] = "Memory limit: {$memoryLimit}";
    echo "✅ Memory Limit: {$memoryLimit} (Recommended: >= 256M)\n";
} else {
    $warnings[] = "Memory limit: {$memoryLimit} (Recommended: >= 256M)";
    echo "⚠️  Memory Limit: {$memoryLimit} (Recommended: >= 256M)\n";
}

// Max execution time
$maxExecutionTime = ini_get('max_execution_time');
if ($maxExecutionTime >= 60 || $maxExecutionTime == 0) {
    $success[] = "Max execution time: {$maxExecutionTime}";
    echo "✅ Max Execution Time: {$maxExecutionTime}s (Recommended: >= 60s)\n";
} else {
    $warnings[] = "Max execution time: {$maxExecutionTime}s (Recommended: >= 60s)";
    echo "⚠️  Max Execution Time: {$maxExecutionTime}s (Recommended: >= 60s)\n";
}

// Upload max filesize
$uploadMaxFilesize = ini_get('upload_max_filesize');
echo "ℹ️  Upload Max Filesize: {$uploadMaxFilesize}\n";

// Post max size
$postMaxSize = ini_get('post_max_size');
echo "ℹ️  Post Max Size: {$postMaxSize}\n";

echo "\n🗄️  DATABASE CONNECTION TEST:\n";

// Test MySQL connection if pdo_mysql is available
if (extension_loaded('pdo_mysql')) {
    echo "ℹ️  PDO MySQL extension is available for database connections\n";
    echo "ℹ️  You can test database connection during installation\n";
} else {
    echo "❌ PDO MySQL extension is not available\n";
    echo "   Database connections will not work without this extension\n";
}

echo "\n📊 SUMMARY:\n";
echo "===========\n";

if (count($success) > 0) {
    echo "✅ PASSED (" . count($success) . " items):\n";
    foreach ($success as $item) {
        echo "   • {$item}\n";
    }
    echo "\n";
}

if (count($warnings) > 0) {
    echo "⚠️  WARNINGS (" . count($warnings) . " items):\n";
    foreach ($warnings as $item) {
        echo "   • {$item}\n";
    }
    echo "\n";
}

if (count($errors) > 0) {
    echo "❌ ERRORS (" . count($errors) . " items):\n";
    foreach ($errors as $item) {
        echo "   • {$item}\n";
    }
    echo "\n";
    echo "🚨 CRITICAL: Please fix the errors above before proceeding with installation.\n\n";
    
    echo "🔧 COMMON FIXES:\n";
    echo "================\n";
    echo "For XAMPP:\n";
    echo "1. Edit php.ini file (XAMPP Control Panel > Apache > Config > PHP)\n";
    echo "2. Uncomment required extensions (remove semicolon)\n";
    echo "3. Restart Apache\n\n";
    
    echo "For Linux:\n";
    echo "1. Install missing extensions: sudo apt-get install php-[extension]\n";
    echo "2. Restart web server: sudo systemctl restart apache2\n\n";
    
    exit(1);
} else {
    echo "🎉 CONGRATULATIONS!\n";
    echo "Your server meets all requirements for Laravel Invoice Management System!\n\n";
    echo "🚀 Next steps:\n";
    echo "1. Run the setup script: bash install-server-setup.sh (Linux/Mac) or install-server-setup.bat (Windows)\n";
    echo "2. Visit your domain/install to start the installation wizard\n\n";
    exit(0);
}

function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int) $val;
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}
