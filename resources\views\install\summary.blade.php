@extends('install.layouts.master')

@section('title', 'Installation Summary')

@section('content')
<div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-2xl text-white"></i>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-white">🔥 BEAST MODE Installation Summary</h1>
                        <p class="text-blue-100 mt-1">Review your configuration before finalizing</p>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="px-8 py-6">
                <!-- Configuration Summary -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <!-- Database Configuration -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-database text-blue-600 text-xl mr-3"></i>
                            <h3 class="text-lg font-semibold text-gray-900">Database Configuration</h3>
                        </div>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Connection:</span>
                                <span class="font-medium">{{ $summary['database']['connection'] ?? 'Not configured' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Host:</span>
                                <span class="font-medium">{{ $summary['database']['host'] ?? 'Not configured' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Database:</span>
                                <span class="font-medium">{{ $summary['database']['database'] ?? 'Not configured' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Username:</span>
                                <span class="font-medium">{{ $summary['database']['username'] ?? 'Not configured' }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Mail Configuration -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-envelope text-purple-600 text-xl mr-3"></i>
                            <h3 class="text-lg font-semibold text-gray-900">Mail Configuration</h3>
                        </div>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Mailer:</span>
                                <span class="font-medium">{{ $summary['mail']['mailer'] ?? 'Not configured' }}</span>
                            </div>
                            @if(($summary['mail']['mailer'] ?? '') === 'smtp')
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Host:</span>
                                    <span class="font-medium">{{ $summary['mail']['host'] ?? 'Not configured' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Port:</span>
                                    <span class="font-medium">{{ $summary['mail']['port'] ?? 'Not configured' }}</span>
                                </div>
                            @endif
                            <div class="flex justify-between">
                                <span class="text-gray-600">From Address:</span>
                                <span class="font-medium">{{ $summary['mail']['from_address'] ?? 'Not configured' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">From Name:</span>
                                <span class="font-medium">{{ $summary['mail']['from_name'] ?? 'Not configured' }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Application Configuration -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-cog text-green-600 text-xl mr-3"></i>
                            <h3 class="text-lg font-semibold text-gray-900">Application Settings</h3>
                        </div>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Name:</span>
                                <span class="font-medium">{{ $summary['app']['name'] ?? 'Laravel Invoice System' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">URL:</span>
                                <span class="font-medium">{{ $summary['app']['url'] ?? 'Not configured' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Timezone:</span>
                                <span class="font-medium">{{ $summary['app']['timezone'] ?? 'UTC' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Locale:</span>
                                <span class="font-medium">{{ $summary['app']['locale'] ?? 'en' }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- System Information -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-server text-orange-600 text-xl mr-3"></i>
                            <h3 class="text-lg font-semibold text-gray-900">System Information</h3>
                        </div>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">PHP Version:</span>
                                <span class="font-medium">{{ phpversion() }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Laravel Version:</span>
                                <span class="font-medium">{{ app()->version() }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Server Software:</span>
                                <span class="font-medium">{{ $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Installation Date:</span>
                                <span class="font-medium">{{ now()->format('Y-m-d H:i:s') }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pre-Installation Checklist -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                    <h3 class="font-semibold text-blue-800 mb-4">🔍 Pre-Installation Checklist</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span class="text-blue-700">Server requirements verified</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span class="text-blue-700">File permissions configured</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span class="text-blue-700">Database connection tested</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span class="text-blue-700">Configuration validated</span>
                        </div>
                    </div>
                </div>

                <!-- What Will Happen -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
                    <h3 class="font-semibold text-yellow-800 mb-4">⚡ What Will Happen Next</h3>
                    <div class="space-y-2 text-yellow-700">
                        <div class="flex items-start">
                            <span class="font-medium mr-2">1.</span>
                            <span>Generate application encryption key</span>
                        </div>
                        <div class="flex items-start">
                            <span class="font-medium mr-2">2.</span>
                            <span>Run database migrations to create tables</span>
                        </div>
                        <div class="flex items-start">
                            <span class="font-medium mr-2">3.</span>
                            <span>Seed database with initial data and roles</span>
                        </div>
                        <div class="flex items-start">
                            <span class="font-medium mr-2">4.</span>
                            <span>Optimize application for production</span>
                        </div>
                        <div class="flex items-start">
                            <span class="font-medium mr-2">5.</span>
                            <span>Lock installation to prevent re-installation</span>
                        </div>
                    </div>
                </div>

                <!-- Footer Buttons -->
                <div class="flex items-center justify-between pt-6 border-t">
                    <a href="{{ route('install.branding') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Branding
                    </a>

                    <a href="{{ route('install.finalize') }}"
                       class="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 shadow-lg transform transition hover:scale-105">
                        <i class="fas fa-rocket mr-2"></i>
                        🔥 FINALIZE INSTALLATION (BEAST MODE)
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection