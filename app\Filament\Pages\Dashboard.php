<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\RevenueChartWidget;
use App\Filament\Widgets\OutstandingInvoicesWidget;
use Filament\Facades\Filament;
use Filament\Support\Facades\FilamentIcon;
use Illuminate\Contracts\Support\Htmlable;

class Dashboard extends \Filament\Pages\Dashboard
{
    public static function getNavigationIcon(): string | Htmlable | null
    {
        return static::$navigationIcon
            ?? FilamentIcon::resolve('panels::pages.dashboard.navigation-item')
            ?? (Filament::hasTopNavigation() ? 'heroicon-m-chart-pie' : 'heroicon-o-chart-pie');
    }

    public function getWidgets(): array
    {
        return [
            RevenueChartWidget::class,
            OutstandingInvoicesWidget::class,
            \App\Filament\Widgets\DashboardOverview::class,
        ];
    }
}
