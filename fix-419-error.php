<?php
/**
 * 🔥 BEAST MODE: Quick Fix for 419 Page Expired Error
 * Run this script if you encounter CSRF token issues during installation
 */

echo "🔥 BEAST MODE: 419 Error Fix Script\n";
echo "===================================\n\n";

// Check if we're in a Laravel project
if (!file_exists('artisan')) {
    echo "❌ Error: Not in a Laravel project directory\n";
    echo "Please run this script from your Laravel project root.\n";
    exit(1);
}

echo "🔧 Fixing 419 Page Expired Error...\n\n";

// Step 1: Clear all caches
echo "1️⃣  Clearing application caches...\n";
$commands = [
    'php artisan config:clear',
    'php artisan cache:clear',
    'php artisan view:clear',
    'php artisan route:clear'
];

foreach ($commands as $command) {
    echo "   Running: {$command}\n";
    $output = [];
    $returnCode = 0;
    exec($command . ' 2>&1', $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "   ✅ Success\n";
    } else {
        echo "   ⚠️  Warning: " . implode("\n", $output) . "\n";
    }
}

// Step 2: Check and fix session directory
echo "\n2️⃣  Checking session directory...\n";
$sessionDir = 'storage/framework/sessions';

if (!file_exists($sessionDir)) {
    echo "   Creating session directory...\n";
    mkdir($sessionDir, 0755, true);
    echo "   ✅ Session directory created\n";
} else {
    echo "   ✅ Session directory exists\n";
}

// Check if directory is writable
if (is_writable($sessionDir)) {
    echo "   ✅ Session directory is writable\n";
} else {
    echo "   🔧 Fixing session directory permissions...\n";
    chmod($sessionDir, 0755);
    echo "   ✅ Session directory permissions fixed\n";
}

// Step 3: Check .env file
echo "\n3️⃣  Checking environment configuration...\n";
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    
    // Check session driver
    if (strpos($envContent, 'SESSION_DRIVER=') !== false) {
        echo "   ✅ SESSION_DRIVER is configured\n";
    } else {
        echo "   🔧 Adding SESSION_DRIVER to .env...\n";
        file_put_contents('.env', "\nSESSION_DRIVER=file\n", FILE_APPEND);
        echo "   ✅ SESSION_DRIVER added\n";
    }
    
    // Check app key
    if (strpos($envContent, 'APP_KEY=') !== false && strpos($envContent, 'APP_KEY=base64:') !== false) {
        echo "   ✅ APP_KEY is configured\n";
    } else {
        echo "   🔧 Generating APP_KEY...\n";
        exec('php artisan key:generate --force 2>&1', $output, $returnCode);
        if ($returnCode === 0) {
            echo "   ✅ APP_KEY generated\n";
        } else {
            echo "   ⚠️  Warning: Could not generate APP_KEY automatically\n";
        }
    }
} else {
    echo "   ❌ .env file not found\n";
    if (file_exists('.env.example')) {
        echo "   🔧 Creating .env from .env.example...\n";
        copy('.env.example', '.env');
        echo "   ✅ .env file created\n";
    }
}

// Step 4: Fix file permissions
echo "\n4️⃣  Fixing file permissions...\n";
$directories = [
    'storage',
    'storage/framework',
    'storage/framework/sessions',
    'storage/framework/views',
    'storage/framework/cache',
    'storage/logs',
    'bootstrap/cache'
];

foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
        echo "   ✅ Created directory: {$dir}\n";
    }
    
    if (chmod($dir, 0755)) {
        echo "   ✅ Fixed permissions: {$dir}\n";
    } else {
        echo "   ⚠️  Could not fix permissions: {$dir}\n";
    }
}

// Step 5: Test session functionality
echo "\n5️⃣  Testing session functionality...\n";
session_start();
$_SESSION['test'] = 'working';

if (isset($_SESSION['test']) && $_SESSION['test'] === 'working') {
    echo "   ✅ Session functionality is working\n";
    unset($_SESSION['test']);
} else {
    echo "   ❌ Session functionality is not working\n";
}

session_destroy();

// Step 6: Final recommendations
echo "\n🎯 FINAL STEPS:\n";
echo "===============\n";
echo "1. Restart your web server:\n";
echo "   - Apache: sudo systemctl restart apache2\n";
echo "   - Nginx: sudo systemctl restart nginx\n";
echo "   - XAMPP: Restart Apache from XAMPP Control Panel\n\n";

echo "2. Clear your browser cache and cookies\n\n";

echo "3. Try accessing the installation again:\n";
echo "   http://your-domain.com/install\n\n";

echo "4. If the error persists, check your web server error logs:\n";
echo "   - Apache: /var/log/apache2/error.log\n";
echo "   - Nginx: /var/log/nginx/error.log\n";
echo "   - XAMPP: xampp/apache/logs/error.log\n\n";

echo "🔥 419 Error Fix Complete!\n";
echo "If you still encounter issues, please check the BEAST_MODE_INSTALLATION_GUIDE.md\n";
echo "for more detailed troubleshooting steps.\n";
