<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Collection Rate Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #059669;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .summary-section {
            margin-bottom: 25px;
        }
        .summary-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .summary-item {
            display: table-cell;
            width: 25%;
            padding: 10px;
            border: 1px solid #ddd;
            text-align: center;
            vertical-align: top;
        }
        .summary-item h3 {
            margin: 0 0 5px 0;
            font-size: 14px;
            color: #666;
        }
        .summary-item .value {
            font-size: 18px;
            font-weight: bold;
            color: #059669;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0 10px 0;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .performance-excellent {
            color: #059669;
            font-weight: bold;
        }
        .performance-good {
            color: #f59e0b;
            font-weight: bold;
        }
        .performance-poor {
            color: #dc2626;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .highlight {
            background-color: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Collection Rate Report</h1>
        <p>Generated on: {{ now()->format('F j, Y \a\t g:i A') }}</p>
        @if(isset($filters['start_date']) && isset($filters['end_date']))
        <p>Period: {{ \Carbon\Carbon::parse($filters['start_date'])->format('M j, Y') }} - {{ \Carbon\Carbon::parse($filters['end_date'])->format('M j, Y') }}</p>
        @endif
    </div>

    <!-- Performance Highlight -->
    @if(($data['collection_rate'] ?? 0) >= 80)
    <div class="highlight">
        <strong>Excellent Performance:</strong> Your collection rate of {{ number_format($data['collection_rate'], 1) }}% is above industry standards.
    </div>
    @endif

    <!-- Summary Section -->
    <div class="summary-section">
        <div class="section-title">Collection Performance Summary</div>
        <div class="summary-grid">
            <div class="summary-item">
                <h3>Collection Rate</h3>
                <div class="value">{{ number_format($data['collection_rate'] ?? 0, 1) }}%</div>
            </div>
            <div class="summary-item">
                <h3>Average Collection Time</h3>
                <div class="value">{{ number_format($data['average_collection_time'] ?? 0, 1) }} days</div>
            </div>
            <div class="summary-item">
                <h3>Total Collected</h3>
                <div class="value">{{ getCurrencyAmount($data['total_collected'] ?? 0, true) }}</div>
            </div>
            <div class="summary-item">
                <h3>Collection Efficiency</h3>
                <div class="value">{{ number_format($data['collection_efficiency'] ?? 0, 1) }}%</div>
            </div>
        </div>
    </div>

    <!-- Monthly Collection Trends -->
    @if(isset($data['monthly_trends']) && !empty($data['monthly_trends']))
    <div class="section-title">Monthly Collection Trends</div>
    <table>
        <thead>
            <tr>
                <th>Month</th>
                <th class="text-right">Invoices Issued</th>
                <th class="text-right">Amount Collected</th>
                <th class="text-right">Collection Rate</th>
                <th class="text-right">Avg. Collection Time</th>
            </tr>
        </thead>
        <tbody>
            @foreach($data['monthly_trends'] as $trend)
            <tr>
                <td>{{ $trend['month'] }}</td>
                <td class="text-right">{{ number_format($trend['invoices_issued']) }}</td>
                <td class="text-right">{{ getCurrencyAmount($trend['amount_collected'], true) }}</td>
                <td class="text-right">
                    @if($trend['collection_rate'] >= 80)
                        <span class="performance-excellent">{{ number_format($trend['collection_rate'], 1) }}%</span>
                    @elseif($trend['collection_rate'] >= 60)
                        <span class="performance-good">{{ number_format($trend['collection_rate'], 1) }}%</span>
                    @else
                        <span class="performance-poor">{{ number_format($trend['collection_rate'], 1) }}%</span>
                    @endif
                </td>
                <td class="text-right">{{ number_format($trend['avg_collection_time'], 1) }} days</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <!-- Client Collection Performance -->
    @if(isset($data['client_performance']) && !empty($data['client_performance']))
    <div class="section-title">Client Collection Performance</div>
    <table>
        <thead>
            <tr>
                <th>Client Name</th>
                <th class="text-right">Total Invoiced</th>
                <th class="text-right">Total Collected</th>
                <th class="text-right">Collection Rate</th>
                <th class="text-right">Avg. Payment Time</th>
                <th class="text-center">Performance</th>
            </tr>
        </thead>
        <tbody>
            @foreach($data['client_performance'] as $client)
            <tr>
                <td>{{ $client['client_name'] }}</td>
                <td class="text-right">{{ getCurrencyAmount($client['total_invoiced'], true) }}</td>
                <td class="text-right">{{ getCurrencyAmount($client['total_collected'], true) }}</td>
                <td class="text-right">{{ number_format($client['collection_rate'], 1) }}%</td>
                <td class="text-right">{{ number_format($client['avg_payment_time'], 1) }} days</td>
                <td class="text-center">
                    @if($client['collection_rate'] >= 90)
                        <span class="performance-excellent">Excellent</span>
                    @elseif($client['collection_rate'] >= 70)
                        <span class="performance-good">Good</span>
                    @else
                        <span class="performance-poor">Needs Attention</span>
                    @endif
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <!-- Payment Method Analysis -->
    @if(isset($data['payment_methods']) && !empty($data['payment_methods']))
    <div class="section-title">Payment Method Analysis</div>
    <table>
        <thead>
            <tr>
                <th>Payment Method</th>
                <th class="text-right">Transaction Count</th>
                <th class="text-right">Total Amount</th>
                <th class="text-right">Percentage</th>
                <th class="text-right">Avg. Processing Time</th>
            </tr>
        </thead>
        <tbody>
            @foreach($data['payment_methods'] as $method)
            <tr>
                <td>{{ $method['method'] }}</td>
                <td class="text-right">{{ number_format($method['count']) }}</td>
                <td class="text-right">{{ getCurrencyAmount($method['amount'], true) }}</td>
                <td class="text-right">{{ number_format($method['percentage'], 1) }}%</td>
                <td class="text-right">{{ number_format($method['avg_processing_time'], 1) }} days</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <!-- Collection Recommendations -->
    <div class="section-title">Collection Recommendations</div>
    <table>
        <thead>
            <tr>
                <th>Area</th>
                <th>Current Status</th>
                <th>Recommendation</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Overall Collection Rate</td>
                <td>{{ number_format($data['collection_rate'] ?? 0, 1) }}%</td>
                <td>
                    @if(($data['collection_rate'] ?? 0) >= 85)
                        Maintain current collection practices
                    @elseif(($data['collection_rate'] ?? 0) >= 70)
                        Consider implementing automated reminders
                    @else
                        Review credit policies and collection procedures
                    @endif
                </td>
            </tr>
            <tr>
                <td>Average Collection Time</td>
                <td>{{ number_format($data['average_collection_time'] ?? 0, 1) }} days</td>
                <td>
                    @if(($data['average_collection_time'] ?? 0) <= 30)
                        Excellent payment timing
                    @elseif(($data['average_collection_time'] ?? 0) <= 45)
                        Consider offering early payment discounts
                    @else
                        Implement stricter payment terms
                    @endif
                </td>
            </tr>
        </tbody>
    </table>

    <div class="footer">
        <p>This report was generated automatically by the Invoice Management System</p>
        <p>{{ config('app.name') }} - Collection Rate Analytics</p>
    </div>
</body>
</html>
