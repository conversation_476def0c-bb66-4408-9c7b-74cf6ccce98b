<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center space-x-2">
                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <span>Collection Rate Analytics - {{ $period }}</span>
            </div>
        </x-slot>
        
        <x-slot name="headerEnd">
            <div class="flex items-center space-x-4 text-sm text-gray-500">
                @if($hasData)
                    <div class="flex items-center space-x-1">
                        <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                        <span>{{ $collectionRate }}% Rate</span>
                    </div>
                @else
                    <div class="flex items-center space-x-1">
                        <span class="w-2 h-2 bg-gray-400 rounded-full"></span>
                        <span>No Data</span>
                    </div>
                @endif
            </div>
        </x-slot>

        <div class="space-y-4">
            <!-- KPI Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-green-600 dark:text-green-400">Collection Rate</p>
                            <p class="text-2xl font-bold text-green-900 dark:text-green-100">
                                {{ number_format($collectionRate, 1) }}%
                            </p>
                        </div>
                        <div class="p-2 bg-green-500 rounded-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-blue-600 dark:text-blue-400">Total Invoiced</p>
                            <p class="text-xl font-bold text-blue-900 dark:text-blue-100">
                                {{ getCurrencyAmount($totalInvoiced, true) }}
                            </p>
                        </div>
                        <div class="p-2 bg-blue-500 rounded-lg">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-purple-600 dark:text-purple-400">Total Collected</p>
                            <p class="text-xl font-bold text-purple-900 dark:text-purple-100">
                                {{ getCurrencyAmount($totalCollected, true) }}
                            </p>
                        </div>
                        <div class="p-2 bg-purple-500 rounded-lg">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-orange-600 dark:text-orange-400">Avg Payment Time</p>
                            <p class="text-xl font-bold text-orange-900 dark:text-orange-100">
                                {{ number_format($averagePaymentTime, 1) }} days
                            </p>
                        </div>
                        <div class="p-2 bg-orange-500 rounded-lg">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chart and Statistics -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Chart Container -->
                <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Monthly Collection Trends</h3>
                    @if($hasData)
                        <div class="relative" style="height: 300px;">
                            <canvas id="collectionChart" class="w-full h-full"></canvas>
                        </div>
                    @else
                        <div class="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400">
                            <svg class="w-16 h-16 mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            <h3 class="text-lg font-medium mb-2">No Collection Data</h3>
                            <p class="text-center max-w-md">
                                Start receiving payments to see collection rate analytics.
                            </p>
                            @if(isset($error))
                                <p class="text-red-500 text-sm mt-2">Error: {{ $error }}</p>
                            @endif
                        </div>
                    @endif
                </div>

                <!-- Statistics Panel -->
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Payment Statistics</h3>
                    @if($hasData)
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    <span class="font-medium text-green-800 dark:text-green-200">Paid Invoices</span>
                                </div>
                                <span class="font-bold text-green-900 dark:text-green-100">
                                    {{ $paidInvoicesCount }}
                                </span>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                    <span class="font-medium text-blue-800 dark:text-blue-200">Total Invoices</span>
                                </div>
                                <span class="font-bold text-blue-900 dark:text-blue-100">
                                    {{ $totalInvoicesCount }}
                                </span>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                                    <span class="font-medium text-purple-800 dark:text-purple-200">Success Rate</span>
                                </div>
                                <span class="font-bold text-purple-900 dark:text-purple-100">
                                    {{ $totalInvoicesCount > 0 ? number_format(($paidInvoicesCount / $totalInvoicesCount) * 100, 1) : 0 }}%
                                </span>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                                    <span class="font-medium text-orange-800 dark:text-orange-200">Avg Days to Pay</span>
                                </div>
                                <span class="font-bold text-orange-900 dark:text-orange-100">
                                    {{ number_format($averagePaymentTime, 1) }}
                                </span>
                            </div>
                        </div>
                        
                        <!-- Performance Indicator -->
                        <div class="mt-6 p-4 rounded-lg {{ $collectionRate >= 80 ? 'bg-green-50 dark:bg-green-900/20' : ($collectionRate >= 60 ? 'bg-yellow-50 dark:bg-yellow-900/20' : 'bg-red-50 dark:bg-red-900/20') }}">
                            <div class="flex items-center space-x-2">
                                @if($collectionRate >= 80)
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span class="text-green-800 dark:text-green-200 font-medium">Excellent Performance</span>
                                @elseif($collectionRate >= 60)
                                    <svg class="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    <span class="text-yellow-800 dark:text-yellow-200 font-medium">Good Performance</span>
                                @else
                                    <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span class="text-red-800 dark:text-red-200 font-medium">Needs Improvement</span>
                                @endif
                            </div>
                            <p class="text-sm mt-1 {{ $collectionRate >= 80 ? 'text-green-700 dark:text-green-300' : ($collectionRate >= 60 ? 'text-yellow-700 dark:text-yellow-300' : 'text-red-700 dark:text-red-300') }}">
                                @if($collectionRate >= 80)
                                    Your collection rate is excellent! Keep up the great work.
                                @elseif($collectionRate >= 60)
                                    Your collection rate is good but has room for improvement.
                                @else
                                    Consider implementing follow-up procedures to improve collection rates.
                                @endif
                            </p>
                        </div>
                    @else
                        <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                            <p>No payment data available</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Action Buttons -->
            @if($hasData)
                <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        Period: {{ $period }} • Last updated: {{ now()->format('H:i') }}
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="refreshCollectionChart()" class="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Refresh
                        </button>
                        <a href="{{ route('filament.admin.resources.reports.collection') }}" class="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors text-sm">
                            View Details
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </x-filament::section>

    @if($hasData)
        @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                initializeCollectionChart();
            });

            let collectionChart;

            function initializeCollectionChart() {
                const ctx = document.getElementById('collectionChart');
                if (!ctx) return;

                const chartConfig = {!! $chartConfig !!};
                
                // Destroy existing chart if it exists
                if (collectionChart) {
                    collectionChart.destroy();
                }
                
                try {
                    collectionChart = new Chart(ctx, chartConfig);
                } catch (error) {
                    console.error('Error initializing collection chart:', error);
                    showCollectionChartError();
                }
            }

            function refreshCollectionChart() {
                window.location.reload();
            }

            function showCollectionChartError() {
                const canvas = document.getElementById('collectionChart');
                if (canvas) {
                    const parent = canvas.parentElement;
                    parent.innerHTML = `
                        <div class="flex items-center justify-center h-64 text-red-500">
                            <div class="text-center">
                                <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                <h3 class="text-lg font-medium mb-2">Chart Error</h3>
                                <p>Unable to load the collection rate chart.</p>
                            </div>
                        </div>
                    `;
                }
            }

            // Handle responsive chart resizing
            window.addEventListener('resize', function() {
                if (collectionChart) {
                    collectionChart.resize();
                }
            });
        </script>
        @endpush
    @endif
</x-filament-widgets::widget>
