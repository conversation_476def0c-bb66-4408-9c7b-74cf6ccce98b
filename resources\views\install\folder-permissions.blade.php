@extends('install.layouts.master')

@section('title', 'Folder Permissions')

@section('content')
<div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-lock text-2xl text-white"></i>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-white">Folder Permissions</h1>
                        <p class="text-blue-100 mt-1">Checking write permissions for required directories</p>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="px-8 py-6">
                <!-- Overall Status -->
                <div class="mb-8">
                    @if($allPermissionsOk)
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 text-xl mr-3"></i>
                                <div>
                                    <h3 class="text-lg font-medium text-green-800">All Permissions Set Correctly!</h3>
                                    <p class="text-green-700">All required directories have proper write permissions.</p>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle text-red-500 text-xl mr-3"></i>
                                <div>
                                    <h3 class="text-lg font-medium text-red-800">Permission Issues Found</h3>
                                    <p class="text-red-700">Some directories don't have proper write permissions. Please fix these before continuing.</p>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Directory Permissions -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Directory Permissions</h2>
                    <div class="space-y-4">
                        @foreach ($folderPermissions as $folder => $details)
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="fas fa-folder text-blue-600 mr-3"></i>
                                        <div>
                                            <span class="font-medium">{{ $folder }}</span>
                                            <div class="text-sm text-gray-600">{{ $details['path'] }}</div>
                                            <div class="text-xs text-gray-500">Permission: {{ $details['permission'] }}</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        @if($details['writable'])
                                            <i class="fas fa-check-circle text-green-500 text-xl mr-2"></i>
                                            <span class="text-green-600 font-medium">Writable</span>
                                        @else
                                            <i class="fas fa-times-circle text-red-500 text-xl mr-2"></i>
                                            <span class="text-red-600 font-medium">Not Writable</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Environment File -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Environment File</h2>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-file-alt text-green-600 mr-3"></i>
                                <div>
                                    <span class="font-medium">.env file</span>
                                    <div class="text-sm text-gray-600">{{ base_path('.env') }}</div>
                                    @if($envPermissions['exists'])
                                        <div class="text-xs text-gray-500">Permission: {{ $envPermissions['permission'] }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="flex items-center">
                                @if($envPermissions['exists'] && $envPermissions['writable'])
                                    <i class="fas fa-check-circle text-green-500 text-xl mr-2"></i>
                                    <span class="text-green-600 font-medium">Writable</span>
                                @elseif($envPermissions['exists'])
                                    <i class="fas fa-times-circle text-red-500 text-xl mr-2"></i>
                                    <span class="text-red-600 font-medium">Not Writable</span>
                                @else
                                    <i class="fas fa-exclamation-triangle text-yellow-500 text-xl mr-2"></i>
                                    <span class="text-yellow-600 font-medium">Not Found</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fix Instructions -->
                @if(!$allPermissionsOk)
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">How to Fix Permissions</h2>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <h3 class="font-medium text-yellow-800 mb-3">Command Line (Linux/Mac):</h3>
                            <div class="bg-gray-800 text-green-400 p-3 rounded font-mono text-sm overflow-x-auto">
                                <div>chmod -R 755 storage</div>
                                <div>chmod -R 755 bootstrap/cache</div>
                                <div>chmod 644 .env</div>
                            </div>

                            <h3 class="font-medium text-yellow-800 mb-3 mt-4">cPanel/File Manager:</h3>
                            <ul class="text-sm text-yellow-700 space-y-1">
                                <li>• Right-click on each folder and select "Change Permissions"</li>
                                <li>• Set directories to 755 (rwxr-xr-x)</li>
                                <li>• Set .env file to 644 (rw-r--r--)</li>
                                <li>• Apply permissions recursively for directories</li>
                            </ul>

                            <h3 class="font-medium text-yellow-800 mb-3 mt-4">Windows (XAMPP/WAMP):</h3>
                            <ul class="text-sm text-yellow-700 space-y-1">
                                <li>• Right-click on each folder and select "Properties"</li>
                                <li>• Go to "Security" tab and ensure "Full Control" for your user</li>
                                <li>• Make sure "Read-only" is unchecked</li>
                            </ul>
                        </div>
                    </div>
                @endif

                <!-- Additional Info -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="font-medium text-blue-800 mb-2">Why These Permissions Are Needed:</h3>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• <strong>storage/</strong> - For logs, cache, sessions, and uploaded files</li>
                        <li>• <strong>bootstrap/cache/</strong> - For compiled views and configuration cache</li>
                        <li>• <strong>.env</strong> - For storing configuration settings during installation</li>
                    </ul>
                </div>
            </div>

            <!-- Footer -->
            <div class="bg-gray-50 px-8 py-4">
                <div class="flex items-center justify-between">
                    <a href="{{ route('install.server-requirements') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back
                    </a>

                    <div class="flex items-center space-x-3">
                        <button type="button"
                                onclick="window.location.reload()"
                                class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-sync-alt mr-2"></i>
                            Recheck Permissions
                        </button>

                        @if($allPermissionsOk)
                            <a href="{{ route('install.database') }}"
                               class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Next: Database Setup
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        @else
                            <span class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-400 cursor-not-allowed">
                                Fix Permissions First
                            </span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection