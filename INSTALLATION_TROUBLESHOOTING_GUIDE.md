# Installation Troubleshooting Guide

## 🚨 Common Installation Issues and Solutions

### 1. **Database Connection Failures**

#### **Error**: "could not find driver"
**Cause**: Missing PDO MySQL extension
**Solution**:
```bash
# For XAMPP
1. Edit php.ini file
2. Uncomment: extension=pdo_mysql
3. Restart Apache

# For Ubuntu/Debian
sudo apt-get install php-mysql php-pdo

# For CentOS/RHEL
sudo yum install php-mysql php-pdo
```

#### **Error**: "Access denied for user"
**Cause**: Incorrect database credentials
**Solution**:
1. Verify database username and password
2. Ensure database user has proper permissions
3. Test connection manually:
```sql
mysql -u username -p -h hostname
```

#### **Error**: "Unknown database"
**Cause**: Database doesn't exist
**Solution**:
```sql
CREATE DATABASE your_database_name;
GRANT ALL PRIVILEGES ON your_database_name.* TO 'username'@'localhost';
```

### 2. **CSRF <PERSON>ken / Page Expired Errors**

#### **Error**: "Page expired" when submitting forms
**Cause**: CSRF token expiration or session issues
**Solution**:
1. Clear browser cache and cookies
2. Ensure storage/framework/sessions is writable
3. Check session configuration in .env:
```env
SESSION_DRIVER=file
SESSION_LIFETIME=1440
```

#### **Error**: AJAX requests failing with 419 status
**Cause**: CSRF token mismatch
**Solution**:
- The system now auto-refreshes tokens
- If issues persist, refresh the page and try again

### 3. **File Permission Issues**

#### **Error**: "Permission denied" errors
**Cause**: Incorrect file permissions
**Solution**:
```bash
# Linux/Mac
chmod -R 755 storage
chmod -R 755 bootstrap/cache
chmod 644 .env

# Windows (Run as Administrator)
icacls storage /grant Everyone:F /T
icacls bootstrap\cache /grant Everyone:F /T
```

### 4. **Migration and Seeding Errors**

#### **Error**: Migration fails during admin creation
**Cause**: Database connection or permission issues
**Solution**:
1. Verify database connection is working
2. Check if tables already exist
3. Run manually if needed:
```bash
php artisan migrate --force
php artisan db:seed --force
```

#### **Error**: "Class 'Role' not found"
**Cause**: Seeders haven't run properly
**Solution**:
1. Ensure database seeders complete successfully
2. Check if roles table has data
3. Re-run seeders if needed

### 5. **Server Requirements Issues**

#### **Error**: Missing PHP extensions
**Cause**: Required extensions not installed
**Solution**:
```bash
# Check current extensions
php -m

# Install missing extensions (Ubuntu/Debian)
sudo apt-get install php-{extension-name}

# For XAMPP, edit php.ini and uncomment extensions
```

#### **Error**: Memory limit too low
**Cause**: PHP memory limit insufficient
**Solution**:
```ini
# In php.ini
memory_limit = 256M
```

### 6. **Environment Configuration Issues**

#### **Error**: APP_KEY not set
**Cause**: Application key not generated
**Solution**:
```bash
php artisan key:generate --force
```

#### **Error**: Cache/Config issues
**Cause**: Cached configuration conflicts
**Solution**:
```bash
php artisan config:clear
php artisan cache:clear
php artisan view:clear
```

## 🔧 **Diagnostic Tools**

### Check PHP Extensions
```bash
php check_php_extensions.php
```

### Test Database Connection
```bash
php test_db_connection.php
```

### Check Installation Logs
```bash
tail -f storage/logs/install.log
```

## 📋 **Pre-Installation Checklist**

- [ ] PHP 8.1+ installed
- [ ] Required PHP extensions enabled
- [ ] Database server running
- [ ] Database and user created
- [ ] Web server configured
- [ ] File permissions set correctly
- [ ] Storage directories writable

## 🆘 **Emergency Recovery**

### Reset Installation
1. Delete `storage/installed` file
2. Clear all caches
3. Reset database (if needed)
4. Restart installation process

### Manual Database Setup
```sql
-- Create database
CREATE DATABASE invoices CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user (if needed)
CREATE USER 'invoiceuser'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON invoices.* TO 'invoiceuser'@'localhost';
FLUSH PRIVILEGES;
```

### Manual Migration
```bash
# If installation fails, run manually
php artisan migrate --force
php artisan db:seed --force
php artisan key:generate --force
```

## 📞 **Getting Help**

1. **Check Logs**: Always check `storage/logs/install.log` first
2. **Server Requirements**: Verify all requirements are met
3. **Database Access**: Test database connection independently
4. **File Permissions**: Ensure proper permissions on all directories
5. **PHP Configuration**: Verify PHP extensions and settings

## 🔍 **Debug Mode**

Enable debug mode for detailed error information:
```env
APP_DEBUG=true
LOG_LEVEL=debug
```

**Remember to disable debug mode in production!**

---

## Platform-Specific Notes

### XAMPP (Windows)
- PHP extensions: Edit `xampp/php/php.ini`
- Restart: Use XAMPP Control Panel
- Permissions: Usually not an issue on Windows

### LAMP (Linux)
- PHP extensions: Use package manager
- Restart: `sudo systemctl restart apache2`
- Permissions: Critical - use chmod/chown

### Shared Hosting
- PHP extensions: Contact hosting provider
- File permissions: Use cPanel File Manager
- Database: Use hosting control panel

---

**Note**: This guide covers the most common installation issues. For specific problems, check the installation logs for detailed error information.
