<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Session\TokenMismatchException;
use Symfony\Component\HttpFoundation\Response;

class InstallationCsrf
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 🔥 BEAST MODE: Enhanced CSRF handling for installation
        
        // Skip CSRF for GET requests and AJAX status checks
        if ($request->isMethod('GET') || $request->is('install/status')) {
            return $next($request);
        }

        // For POST requests during installation, handle CSRF more gracefully
        if (!file_exists(storage_path('installed'))) {
            try {
                // Ensure session is started
                if (!$request->hasSession()) {
                    $request->setLaravelSession(app('session.store'));
                }

                // Get tokens
                $sessionToken = $request->session()->token();
                $requestToken = $request->input('_token') ?: $request->header('X-CSRF-TOKEN');

                // If no session token exists, generate one
                if (!$sessionToken) {
                    $request->session()->regenerateToken();
                    $sessionToken = $request->session()->token();
                }

                // If tokens don't match, try to recover
                if (!hash_equals($sessionToken, $requestToken)) {
                    // Log the mismatch for debugging
                    \Log::warning('CSRF token mismatch during installation', [
                        'session_token' => $sessionToken,
                        'request_token' => $requestToken,
                        'url' => $request->url(),
                        'method' => $request->method()
                    ]);

                    // For AJAX requests, return JSON error with new token
                    if ($request->expectsJson()) {
                        return response()->json([
                            'success' => false,
                            'error' => 'Session expired. Please refresh the page and try again.',
                            'csrf_token' => $sessionToken,
                            'code' => 419
                        ], 419);
                    }

                    // For regular form submissions, redirect back with error
                    return redirect()->back()
                        ->withErrors(['csrf' => 'Your session has expired. Please try again.'])
                        ->withInput();
                }

            } catch (\Exception $e) {
                \Log::error('CSRF middleware error during installation', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                // If there's any error, allow the request to proceed during installation
                // This is a fallback to prevent installation from being completely blocked
                return $next($request);
            }
        }

        return $next($request);
    }
}
