@extends('install.layouts.master')

@section('title', 'Welcome')

@section('content')
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-2xl w-full space-y-8">
        <!-- Welcome Card -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-rocket text-3xl text-white"></i>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-white">Welcome to Laravel Invoice System</h1>
                        <p class="text-blue-100 mt-1">Professional invoicing made simple</p>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="px-8 py-6">
                <div class="space-y-6">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900 mb-3">Installation Wizard</h2>
                        <p class="text-gray-600 leading-relaxed">
                            This installation wizard will guide you through the setup process for your Laravel Invoice Management System.
                            The process is simple and should take only a few minutes to complete.
                        </p>
                    </div>

                    <!-- Features -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-green-500 mt-1"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">System Requirements Check</h3>
                                <p class="text-sm text-gray-600">Verify PHP version and extensions</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-database text-blue-500 mt-1"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Database Configuration</h3>
                                <p class="text-sm text-gray-600">Setup MySQL or PostgreSQL connection</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-envelope text-purple-500 mt-1"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Email Configuration</h3>
                                <p class="text-sm text-gray-600">Configure SMTP for notifications</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-user-shield text-red-500 mt-1"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Admin Account</h3>
                                <p class="text-sm text-gray-600">Create your administrator account</p>
                            </div>
                        </div>
                    </div>

                    <!-- Requirements -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="font-medium text-gray-900 mb-2">Before You Begin</h3>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li class="flex items-center">
                                <i class="fas fa-dot-circle text-gray-400 mr-2"></i>
                                Ensure you have PHP 8.1 or higher installed
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-dot-circle text-gray-400 mr-2"></i>
                                Have your database credentials ready
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-dot-circle text-gray-400 mr-2"></i>
                                SMTP settings for email notifications (optional)
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-dot-circle text-gray-400 mr-2"></i>
                                Write permissions for storage and bootstrap/cache folders
                            </li>
                        </ul>
                    </div>

                    <!-- Installation Time -->
                    <div class="flex items-center justify-between text-sm text-gray-500 bg-blue-50 rounded-lg p-3">
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-2"></i>
                            <span>Estimated installation time: 5-10 minutes</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-shield-alt mr-2"></i>
                            <span>Secure installation process</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="bg-gray-50 px-8 py-4">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        Ready to get started?
                    </div>
                    <a href="{{ route('install.server-requirements') }}"
                       class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i class="fas fa-play mr-2"></i>
                        Start Installation
                    </a>
                </div>
            </div>
        </div>

        <!-- Support Info -->
        <div class="text-center">
            <p class="text-sm text-gray-500">
                Need help? Check our
                <a href="#" class="text-blue-600 hover:text-blue-500">documentation</a>
                or
                <a href="#" class="text-blue-600 hover:text-blue-500">contact support</a>
            </p>
        </div>
    </div>
</div>
@endsection