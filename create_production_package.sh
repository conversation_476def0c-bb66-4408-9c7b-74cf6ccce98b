#!/bin/bash

# Invoice Management System - Production Package Creator
# This script creates an optimized production package ready for deployment

set -e  # Exit on any error

# Configuration
PACKAGE_NAME="invoice-system-production-$(date +%Y%m%d_%H%M%S)"
TEMP_DIR="/tmp/$PACKAGE_NAME"
CURRENT_DIR=$(pwd)

echo "🚀 Creating production package: $PACKAGE_NAME"
echo "=================================================="

# Create temporary directory
mkdir -p "$TEMP_DIR"

echo "📁 Copying application files..."

# Copy essential application files
cp -r app "$TEMP_DIR/"
cp -r bootstrap "$TEMP_DIR/"
cp -r config "$TEMP_DIR/"
cp -r database "$TEMP_DIR/"
cp -r public "$TEMP_DIR/"
cp -r resources "$TEMP_DIR/"
cp -r routes "$TEMP_DIR/"

# Create storage directories with proper structure
mkdir -p "$TEMP_DIR/storage/app/public"
mkdir -p "$TEMP_DIR/storage/framework/cache/data"
mkdir -p "$TEMP_DIR/storage/framework/sessions"
mkdir -p "$TEMP_DIR/storage/framework/views"
mkdir -p "$TEMP_DIR/storage/logs"

# Copy essential root files
cp artisan "$TEMP_DIR/"
cp composer.json "$TEMP_DIR/"
cp composer.lock "$TEMP_DIR/"
cp .env.example "$TEMP_DIR/"

# Copy documentation
cp PRODUCTION_DEPLOYMENT_GUIDE.md "$TEMP_DIR/"
cp README.md "$TEMP_DIR/" 2>/dev/null || echo "README.md not found, skipping..."

echo "🔧 Installing production dependencies..."

# Navigate to temp directory and install dependencies
cd "$TEMP_DIR"

# Install composer dependencies (production only)
composer install --no-dev --optimize-autoloader --no-interaction

echo "🧹 Cleaning up development files..."

# Remove development files and directories
rm -rf tests/
rm -rf .git/
rm -rf .github/
rm -rf node_modules/
rm -rf .vscode/
rm -rf .idea/

# Remove development configuration files
rm -f .gitignore
rm -f .gitattributes
rm -f .editorconfig
rm -f phpunit.xml
rm -f vite.config.js
rm -f package.json
rm -f package-lock.json
rm -f tailwind.config.js
rm -f postcss.config.js

echo "⚡ Optimizing for production..."

# Clear any existing caches
php artisan config:clear 2>/dev/null || true
php artisan route:clear 2>/dev/null || true
php artisan view:clear 2>/dev/null || true
php artisan cache:clear 2>/dev/null || true

# Optimize Composer autoloader
composer dump-autoload --optimize --no-dev

echo "📝 Creating deployment instructions..."

# Create a quick deployment script
cat > deploy.sh << 'EOF'
#!/bin/bash

echo "🚀 Invoice Management System - Quick Deployment"
echo "=============================================="

# Check PHP version
PHP_VERSION=$(php -r "echo PHP_VERSION;")
echo "PHP Version: $PHP_VERSION"

if php -r "exit(version_compare(PHP_VERSION, '8.1.0', '<') ? 1 : 0);"; then
    echo "❌ Error: PHP 8.1 or higher is required. Current version: $PHP_VERSION"
    exit 1
fi

echo "✅ PHP version check passed"

# Generate application key if not exists
if [ ! -f .env ]; then
    echo "📝 Creating environment file..."
    cp .env.example .env
    php artisan key:generate
    echo "⚠️  Please edit .env file with your database and mail settings"
else
    echo "✅ Environment file exists"
fi

# Check if we can run artisan commands
if php artisan --version > /dev/null 2>&1; then
    echo "✅ Laravel application is accessible"
    
    # Run migrations
    echo "🗄️  Running database migrations..."
    php artisan migrate --force
    
    # Seed database
    echo "🌱 Seeding database..."
    php artisan db:seed --force
    
    # Create storage link
    echo "🔗 Creating storage link..."
    php artisan storage:link
    
    # Cache configuration
    echo "⚡ Caching configuration..."
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    
    echo "✅ Deployment completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Configure your web server to point to the 'public' directory"
    echo "2. Set proper file permissions (see PRODUCTION_DEPLOYMENT_GUIDE.md)"
    echo "3. Create your first admin user: php artisan make:filament-user"
    echo "4. Access your application at your configured domain"
    
else
    echo "❌ Error: Cannot run Laravel artisan commands"
    echo "Please check your PHP configuration and file permissions"
    exit 1
fi
EOF

chmod +x deploy.sh

echo "📋 Creating file permissions script..."

# Create file permissions script
cat > set_permissions.sh << 'EOF'
#!/bin/bash

echo "🔒 Setting proper file permissions..."

# Set ownership (adjust www-data to your web server user)
WEB_USER=${1:-www-data}

if id "$WEB_USER" &>/dev/null; then
    echo "Setting ownership to $WEB_USER..."
    sudo chown -R $WEB_USER:$WEB_USER .
else
    echo "⚠️  Web user '$WEB_USER' not found. Please run manually:"
    echo "sudo chown -R your-web-user:your-web-user ."
fi

# Set directory permissions
echo "Setting directory permissions..."
find . -type d -exec chmod 755 {} \;

# Set file permissions
echo "Setting file permissions..."
find . -type f -exec chmod 644 {} \;

# Set executable permissions for artisan
chmod +x artisan

# Set writable permissions for storage and cache
echo "Setting writable permissions for storage and cache..."
chmod -R 775 storage/
chmod -R 775 bootstrap/cache/

# Secure .env file
if [ -f .env ]; then
    chmod 600 .env
fi

echo "✅ File permissions set successfully!"
EOF

chmod +x set_permissions.sh

echo "📦 Creating production package archive..."

# Go back to original directory
cd "$CURRENT_DIR"

# Create tar.gz archive
tar -czf "${PACKAGE_NAME}.tar.gz" -C "/tmp" "$PACKAGE_NAME"

# Create zip archive (for Windows users)
cd "/tmp"
zip -r "${CURRENT_DIR}/${PACKAGE_NAME}.zip" "$PACKAGE_NAME" -q

cd "$CURRENT_DIR"

# Clean up temporary directory
rm -rf "$TEMP_DIR"

echo ""
echo "✅ Production package created successfully!"
echo "=================================================="
echo "📦 Package files:"
echo "   - ${PACKAGE_NAME}.tar.gz (Linux/Mac)"
echo "   - ${PACKAGE_NAME}.zip (Windows/Universal)"
echo ""
echo "📁 Package size:"
ls -lh "${PACKAGE_NAME}.tar.gz" "${PACKAGE_NAME}.zip"
echo ""
echo "🚀 Deployment instructions:"
echo "1. Upload and extract the package to your server"
echo "2. Run: chmod +x deploy.sh && ./deploy.sh"
echo "3. Run: chmod +x set_permissions.sh && ./set_permissions.sh"
echo "4. Follow the instructions in PRODUCTION_DEPLOYMENT_GUIDE.md"
echo ""
echo "📖 For detailed instructions, see PRODUCTION_DEPLOYMENT_GUIDE.md"
echo "=================================================="
