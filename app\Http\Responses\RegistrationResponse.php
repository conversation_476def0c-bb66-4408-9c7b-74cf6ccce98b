<?php

namespace App\Http\Responses;

use App\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Filament\Http\Responses\Auth\Contracts\RegistrationResponse as RegistrationResponseContract;

class RegistrationResponse implements RegistrationResponseContract
{
    public function toResponse($request)
    {
        /** @var User $user */
        $user = auth()->user();

        if (!$user) {
            Log::warning('RegistrationResponse: No authenticated user found after registration');
            return redirect()->route('filament.admin.auth.login');
        }

        // Log registration info for debugging
        Log::info('RegistrationResponse: User registered', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_name' => $user->name
        ]);

        // Assign default role to new users
        try {
            if ($user->roles()->count() === 0) {
                // For new registrations, assign client role by default
                // Admin users should be created through the admin interface
                $clientRole = Role::where('name', Role::ROLE_CLIENT)->first();
                
                if ($clientRole) {
                    $user->assignRole($clientRole);
                    Log::info('RegistrationResponse: Assigned client role to new user', [
                        'user_id' => $user->id
                    ]);
                    
                    // Redirect to client dashboard
                    return redirect()->route('filament.client.pages.dashboard');
                } else {
                    Log::warning('RegistrationResponse: Client role not found, assigning admin role');
                    
                    // Fallback to admin role if client role doesn't exist
                    $adminRole = Role::where('name', Role::ROLE_ADMIN)->first();
                    if ($adminRole) {
                        $user->assignRole($adminRole);
                        return redirect()->route('filament.admin.pages.dashboard');
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('RegistrationResponse: Failed to assign role to new user', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }

        // Check existing roles and redirect accordingly
        $roles = $user->roles()->get();
        
        foreach ($roles as $role) {
            if ($role->name === Role::ROLE_ADMIN) {
                return redirect()->route('filament.admin.pages.dashboard');
            }

            if ($role->name === Role::ROLE_CLIENT) {
                return redirect()->route('filament.client.pages.dashboard');
            }
        }

        // Default fallback to admin dashboard
        Log::warning('RegistrationResponse: No matching roles found, defaulting to admin dashboard', [
            'user_id' => $user->id,
            'roles' => $roles->pluck('name')->toArray()
        ]);
        
        return redirect()->route('filament.admin.pages.dashboard');
    }
}
