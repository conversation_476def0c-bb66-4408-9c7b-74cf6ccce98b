# Invoice Management System - Production Deployment Guide

## System Requirements

### Server Requirements
- **PHP Version**: 8.1 or higher
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **Database**: MySQL 8.0+ or MariaDB 10.4+
- **Memory**: Minimum 512MB RAM (1GB+ recommended)
- **Storage**: Minimum 1GB free space

### Required PHP Extensions
```bash
# Core Extensions (usually enabled by default)
- php-cli
- php-fpm (for Nginx)
- php-mysql
- php-pdo
- php-mbstring
- php-xml
- php-curl
- php-zip
- php-gd
- php-json
- php-tokenizer
- php-fileinfo
- php-openssl
- php-bcmath
- php-ctype

# Additional Extensions for Laravel
- php-intl (for internationalization)
- php-redis (if using Redis cache)
```

## Pre-Deployment Checklist

### 1. Environment Setup
- [ ] PHP 8.1+ installed and configured
- [ ] MySQL/MariaDB database created
- [ ] Web server configured with proper document root
- [ ] SSL certificate installed (recommended)
- [ ] Domain/subdomain configured

### 2. File Permissions
```bash
# Set proper ownership (replace 'www-data' with your web server user)
sudo chown -R www-data:www-data /path/to/your/application
sudo chmod -R 755 /path/to/your/application
sudo chmod -R 775 /path/to/your/application/storage
sudo chmod -R 775 /path/to/your/application/bootstrap/cache
```

### 3. Database Setup
- [ ] Database created with proper collation (utf8mb4_unicode_ci)
- [ ] Database user created with appropriate privileges
- [ ] Database connection tested

## Deployment Steps

### Step 1: Upload Application Files
1. Upload all application files to your web server
2. Ensure the document root points to the `public` directory
3. Verify file permissions are set correctly

### Step 2: Environment Configuration
1. Copy `.env.example` to `.env`
2. Configure the following environment variables:

```env
# Application Settings
APP_NAME="Invoice Management System"
APP_ENV=production
APP_KEY=base64:YOUR_APP_KEY_HERE
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_database_user
DB_PASSWORD=your_database_password

# Mail Configuration (for notifications)
MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Cache Configuration
CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_CONNECTION=sync

# File Storage
FILESYSTEM_DISK=local
```

### Step 3: Install Dependencies and Generate Key
```bash
# Navigate to application directory
cd /path/to/your/application

# Install Composer dependencies (production only)
composer install --optimize-autoloader --no-dev

# Generate application key
php artisan key:generate

# Clear and cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### Step 4: Database Migration and Seeding
```bash
# Run database migrations
php artisan migrate --force

# Seed the database with initial data
php artisan db:seed --force

# Create storage link for file uploads
php artisan storage:link
```

### Step 5: Web Server Configuration

#### Apache Configuration (.htaccess)
Create/verify `.htaccess` in the public directory:
```apache
<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
```

#### Nginx Configuration
```nginx
server {
    listen 80;
    listen [::]:80;
    server_name yourdomain.com;
    root /path/to/your/application/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

## Post-Deployment Configuration

### 1. Admin User Setup
```bash
# Create the first admin user
php artisan make:filament-user
```

### 2. Application Settings
1. Log in to the admin panel at `https://yourdomain.com/admin`
2. Navigate to Settings and configure:
   - Company information
   - Invoice settings
   - Email templates
   - Currency settings
   - Tax rates

### 3. Security Hardening
- [ ] Remove `.env.example` file
- [ ] Ensure `.env` file is not publicly accessible
- [ ] Configure proper file permissions
- [ ] Enable HTTPS and redirect HTTP traffic
- [ ] Configure firewall rules
- [ ] Set up regular backups

### 4. Performance Optimization
```bash
# Optimize Composer autoloader
composer dump-autoload --optimize

# Cache configuration, routes, and views
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Enable OPcache in PHP configuration
# Add to php.ini:
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

## Shared Hosting Specific Instructions

### cPanel/Shared Hosting Setup
1. **Upload Files**: Upload to `public_html` or create a subdirectory
2. **Document Root**: If using subdirectory, point domain to `/subdirectory/public`
3. **PHP Version**: Ensure PHP 8.1+ is selected in cPanel
4. **Database**: Create MySQL database and user through cPanel
5. **Cron Jobs**: Set up for scheduled tasks (if needed)

### File Structure for Shared Hosting
```
public_html/
├── invoices/           # Application root
│   ├── app/
│   ├── bootstrap/
│   ├── config/
│   ├── database/
│   ├── resources/
│   ├── routes/
│   ├── storage/
│   ├── vendor/
│   ├── .env
│   └── ...
└── public/             # Web accessible directory
    ├── index.php       # Copy from invoices/public/
    ├── .htaccess       # Copy from invoices/public/
    └── assets/         # Copy from invoices/public/
```

## Troubleshooting

### Common Issues
1. **500 Internal Server Error**
   - Check file permissions
   - Verify `.env` configuration
   - Check error logs

2. **Database Connection Error**
   - Verify database credentials
   - Check database server status
   - Ensure database exists

3. **Missing Dependencies**
   - Run `composer install --no-dev`
   - Check PHP extensions

4. **File Upload Issues**
   - Check storage directory permissions
   - Verify `storage:link` was created
   - Check PHP upload limits

### Log Files
- Application logs: `storage/logs/laravel.log`
- Web server logs: Check your hosting provider's documentation
- PHP error logs: Usually in `/var/log/php_errors.log`

## Maintenance

### Regular Tasks
- [ ] Database backups (daily recommended)
- [ ] File backups (weekly recommended)
- [ ] Log file rotation
- [ ] Security updates
- [ ] Performance monitoring

### Update Process
1. Backup database and files
2. Upload new application files
3. Run migrations: `php artisan migrate --force`
4. Clear caches: `php artisan cache:clear`
5. Test functionality

## Support

For technical support or issues:
1. Check the troubleshooting section above
2. Review application logs
3. Consult Laravel documentation
4. Contact your hosting provider for server-related issues

---

**Important**: Always test the deployment in a staging environment before deploying to production.
