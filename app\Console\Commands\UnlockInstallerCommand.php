<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class UnlockInstallerCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'setup:unlock';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Unlock the installation wizard';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (file_exists(storage_path('installed'))) {
            unlink(storage_path('installed'));
            $this->info('Installation wizard unlocked.');
        } else {
            $this->info('Installation wizard is not locked.');
        }
    }
}
