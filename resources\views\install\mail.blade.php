@extends('install.layouts.master')

@section('title', 'Mail Configuration')

@section('content')
<div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-envelope text-2xl text-white"></i>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-white">Mail Configuration</h1>
                        <p class="text-blue-100 mt-1">Configure email settings for notifications</p>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="px-8 py-6">
                <form action="{{ route('install.mail') }}" method="POST" class="space-y-6" x-data="mailForm()">
                    @csrf

                    <!-- Mail Driver -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-3">Mail Service</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <label class="relative">
                                <input type="radio" name="mail_mailer" value="smtp" x-model="form.mail_mailer" class="sr-only">
                                <div class="border-2 rounded-lg p-4 cursor-pointer transition-all duration-200"
                                     :class="form.mail_mailer === 'smtp' ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'">
                                    <div class="flex items-center">
                                        <i class="fas fa-server text-blue-500 text-xl mr-3"></i>
                                        <div>
                                            <div class="font-medium">SMTP</div>
                                            <div class="text-sm text-gray-500">Custom SMTP server</div>
                                        </div>
                                    </div>
                                </div>
                            </label>
                            <label class="relative">
                                <input type="radio" name="mail_mailer" value="log" x-model="form.mail_mailer" class="sr-only">
                                <div class="border-2 rounded-lg p-4 cursor-pointer transition-all duration-200"
                                     :class="form.mail_mailer === 'log' ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'">
                                    <div class="flex items-center">
                                        <i class="fas fa-file-alt text-gray-500 text-xl mr-3"></i>
                                        <div>
                                            <div class="font-medium">Log Only</div>
                                            <div class="text-sm text-gray-500">For testing (no emails sent)</div>
                                        </div>
                                    </div>
                                </div>
                            </label>
                        </div>
                        @error('mail_mailer')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- SMTP Configuration -->
                    <div x-show="form.mail_mailer === 'smtp'" x-transition class="space-y-6">
                        <!-- Common SMTP Providers -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">Quick Setup (Optional)</label>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                <button type="button" @click="setProvider('gmail')" class="p-3 border border-gray-300 rounded-lg hover:bg-gray-50 text-sm">
                                    <i class="fab fa-google text-red-500 mr-2"></i>Gmail
                                </button>
                                <button type="button" @click="setProvider('outlook')" class="p-3 border border-gray-300 rounded-lg hover:bg-gray-50 text-sm">
                                    <i class="fab fa-microsoft text-blue-500 mr-2"></i>Outlook
                                </button>
                                <button type="button" @click="setProvider('yahoo')" class="p-3 border border-gray-300 rounded-lg hover:bg-gray-50 text-sm">
                                    <i class="fab fa-yahoo text-purple-500 mr-2"></i>Yahoo
                                </button>
                            </div>
                        </div>

                        <!-- SMTP Settings -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="mail_host" class="block text-sm font-medium text-gray-700">SMTP Host</label>
                                <input type="text"
                                       id="mail_host"
                                       name="mail_host"
                                       value="{{ old('mail_host') }}"
                                       x-model="form.mail_host"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                       placeholder="smtp.gmail.com">
                                @error('mail_host')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="mail_port" class="block text-sm font-medium text-gray-700">Port</label>
                                <input type="number"
                                       id="mail_port"
                                       name="mail_port"
                                       value="{{ old('mail_port', '587') }}"
                                       x-model="form.mail_port"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                       placeholder="587">
                                @error('mail_port')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="mail_user" class="block text-sm font-medium text-gray-700">Username</label>
                                <input type="text"
                                       id="mail_user"
                                       name="mail_user"
                                       value="{{ old('mail_user') }}"
                                       x-model="form.mail_user"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                       placeholder="<EMAIL>">
                                @error('mail_user')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="mail_pass" class="block text-sm font-medium text-gray-700">Password</label>
                                <input type="password"
                                       id="mail_pass"
                                       name="mail_pass"
                                       value="{{ old('mail_pass') }}"
                                       x-model="form.mail_pass"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                       placeholder="Your email password or app password">
                                @error('mail_pass')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <label for="mail_encryption" class="block text-sm font-medium text-gray-700">Encryption</label>
                            <select id="mail_encryption"
                                    name="mail_encryption"
                                    x-model="form.mail_encryption"
                                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                <option value="">None</option>
                                <option value="tls" {{ old('mail_encryption') === 'tls' ? 'selected' : '' }}>TLS</option>
                                <option value="ssl" {{ old('mail_encryption') === 'ssl' ? 'selected' : '' }}>SSL</option>
                            </select>
                            @error('mail_encryption')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- From Address -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="mail_from" class="block text-sm font-medium text-gray-700">From Email Address</label>
                            <input type="email"
                                   id="mail_from"
                                   name="mail_from"
                                   value="{{ old('mail_from') }}"
                                   x-model="form.mail_from"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="<EMAIL>">
                            @error('mail_from')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="mail_from_name" class="block text-sm font-medium text-gray-700">From Name</label>
                            <input type="text"
                                   id="mail_from_name"
                                   name="mail_from_name"
                                   value="{{ old('mail_from_name', 'Invoice System') }}"
                                   x-model="form.mail_from_name"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="Your Company Name">
                            @error('mail_from_name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Test Mail -->
                    <div x-show="form.mail_mailer === 'smtp'" class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-medium text-gray-900">Test Email Configuration</h3>
                            <button type="button"
                                    @click="testMail()"
                                    :disabled="testing"
                                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
                                <i class="fas fa-paper-plane mr-2" :class="{'loading-spinner': testing}"></i>
                                <span x-text="testing ? 'Testing...' : 'Test Connection'"></span>
                            </button>
                        </div>

                        <div x-show="testResult" class="mt-3">
                            <div x-show="testResult && testResult.success" class="bg-green-50 border border-green-200 rounded p-3">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span class="text-green-800 font-medium">Mail configuration is working!</span>
                                </div>
                            </div>
                            <div x-show="testResult && !testResult.success" class="bg-red-50 border border-red-200 rounded p-3">
                                <div class="flex items-start">
                                    <i class="fas fa-times-circle text-red-500 mr-2 mt-0.5"></i>
                                    <div>
                                        <div class="text-red-800 font-medium">Mail test failed</div>
                                        <div class="text-red-700 text-sm mt-1" x-text="testResult.error"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Error Messages -->
                    @if($errors->any())
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex">
                                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
                                <div>
                                    <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                                    <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Help -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h3 class="font-medium text-blue-800 mb-2">Email Setup Tips:</h3>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• For Gmail, use App Passwords instead of your regular password</li>
                            <li>• TLS encryption is recommended for most providers (port 587)</li>
                            <li>• SSL encryption typically uses port 465</li>
                            <li>• You can skip email setup and configure it later if needed</li>
                        </ul>
                    </div>

                    <!-- Footer Buttons -->
                    <div class="flex items-center justify-between pt-6 border-t">
                        <a href="{{ route('install.database') }}"
                           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back
                        </a>

                        <button type="submit"
                                class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Save & Continue
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function mailForm() {
    return {
        form: {
            mail_mailer: 'smtp',
            mail_host: '',
            mail_port: '587',
            mail_user: '',
            mail_pass: '',
            mail_encryption: 'tls',
            mail_from: '',
            mail_from_name: 'Invoice System'
        },
        testing: false,
        testResult: null,

        setProvider(provider) {
            const providers = {
                gmail: {
                    mail_host: 'smtp.gmail.com',
                    mail_port: '587',
                    mail_encryption: 'tls'
                },
                outlook: {
                    mail_host: 'smtp-mail.outlook.com',
                    mail_port: '587',
                    mail_encryption: 'tls'
                },
                yahoo: {
                    mail_host: 'smtp.mail.yahoo.com',
                    mail_port: '587',
                    mail_encryption: 'tls'
                }
            };

            if (providers[provider]) {
                Object.assign(this.form, providers[provider]);
            }
        },

        async testMail() {
            if (this.form.mail_mailer !== 'smtp') return;

            this.testing = true;
            this.testResult = null;

            try {
                const response = await makeRequest('{{ route("install.test-mail") }}', this.form);
                this.testResult = response;
            } catch (error) {
                this.testResult = {
                    success: false,
                    error: 'Network error occurred while testing mail configuration'
                };
            }

            this.testing = false;
        }
    }
}
</script>
@endpush
@endsection