<?php

namespace App\Filament\Widgets;

use App\Services\ChartService;
use App\Services\ReportingService;
use Filament\Widgets\Widget;

class OutstandingInvoicesWidget extends Widget
{
    protected static string $view = 'filament.widgets.outstanding-invoices';
    
    protected int | string | array $columnSpan = 'full';
    
    protected static ?int $sort = 2;
    
    public function getViewData(): array
    {
        $reportingService = app(ReportingService::class);
        $chartService = app(ChartService::class);
        
        try {
            $outstandingData = $reportingService->getOutstandingAnalytics();
            $chartConfig = $chartService->generateOutstandingChart($outstandingData);
            
            return [
                'chartConfig' => json_encode($chartConfig),
                'totalOutstanding' => $outstandingData['total_outstanding'] ?? 0,
                'overdueAmount' => $outstandingData['overdue_amount'] ?? 0,
                'unpaidCount' => $outstandingData['unpaid_count'] ?? 0,
                'overdueCount' => $outstandingData['overdue_count'] ?? 0,
                'statusBreakdown' => $outstandingData['status_breakdown'] ?? [],
                'hasData' => !empty($outstandingData['status_breakdown'])
            ];
        } catch (\Exception $e) {
            return [
                'chartConfig' => json_encode($this->getFallbackChartConfig()),
                'totalOutstanding' => 0,
                'overdueAmount' => 0,
                'unpaidCount' => 0,
                'overdueCount' => 0,
                'statusBreakdown' => [],
                'hasData' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function getFallbackChartConfig(): array
    {
        return [
            'type' => 'doughnut',
            'data' => [
                'labels' => ['No Data'],
                'datasets' => [
                    [
                        'data' => [1],
                        'backgroundColor' => ['#e5e7eb'],
                        'borderWidth' => 0
                    ]
                ]
            ],
            'options' => [
                'responsive' => true,
                'maintainAspectRatio' => false,
                'plugins' => [
                    'legend' => [
                        'display' => false
                    ]
                ]
            ]
        ];
    }
}
