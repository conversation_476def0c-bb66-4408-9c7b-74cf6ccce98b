<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Revenue Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #2563eb;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .summary-section {
            margin-bottom: 25px;
        }
        .summary-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .summary-item {
            display: table-cell;
            width: 25%;
            padding: 10px;
            border: 1px solid #ddd;
            text-align: center;
            vertical-align: top;
        }
        .summary-item h3 {
            margin: 0 0 5px 0;
            font-size: 14px;
            color: #666;
        }
        .summary-item .value {
            font-size: 18px;
            font-weight: bold;
            color: #2563eb;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0 10px 0;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .text-right {
            text-align: right;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .currency-section, .client-section {
            margin-bottom: 25px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Revenue Report</h1>
        <p>Generated on: {{ now()->format('F j, Y \a\t g:i A') }}</p>
        @if(isset($filters['start_date']) && isset($filters['end_date']))
        <p>Period: {{ \Carbon\Carbon::parse($filters['start_date'])->format('M j, Y') }} - {{ \Carbon\Carbon::parse($filters['end_date'])->format('M j, Y') }}</p>
        @endif
        @if(isset($filters['currency_id']) && $filters['currency_id'])
        <p>Currency Filter Applied</p>
        @endif
    </div>

    <!-- Summary Section -->
    <div class="summary-section">
        <div class="section-title">Revenue Summary</div>
        <div class="summary-grid">
            <div class="summary-item">
                <h3>Total Revenue</h3>
                <div class="value">{{ getCurrencyAmount($data['total_revenue'] ?? 0, true) }}</div>
            </div>
            <div class="summary-item">
                <h3>Total Payments</h3>
                <div class="value">{{ number_format($data['payment_count'] ?? 0) }}</div>
            </div>
            <div class="summary-item">
                <h3>Average Payment</h3>
                <div class="value">{{ getCurrencyAmount($data['average_payment'] ?? 0, true) }}</div>
            </div>
            <div class="summary-item">
                <h3>Currencies</h3>
                <div class="value">{{ count($data['revenue_by_currency'] ?? []) }}</div>
            </div>
        </div>
    </div>

    <!-- Revenue by Currency -->
    @if(isset($data['revenue_by_currency']) && !empty($data['revenue_by_currency']))
    <div class="currency-section">
        <div class="section-title">Revenue by Currency</div>
        <table>
            <thead>
                <tr>
                    <th>Currency</th>
                    <th class="text-right">Total Revenue</th>
                    <th class="text-right">Payment Count</th>
                    <th class="text-right">Percentage</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['revenue_by_currency'] as $currency)
                <tr>
                    <td>{{ $currency['currency'] }}</td>
                    <td class="text-right">{{ getCurrencyAmount($currency['total'], true) }}</td>
                    <td class="text-right">{{ number_format($currency['count']) }}</td>
                    <td class="text-right">{{ number_format($currency['percentage'], 1) }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <!-- Revenue by Client -->
    @if(isset($data['revenue_by_client']) && !empty($data['revenue_by_client']))
    <div class="client-section">
        <div class="section-title">Top Clients by Revenue</div>
        <table>
            <thead>
                <tr>
                    <th>Client Name</th>
                    <th class="text-right">Total Revenue</th>
                    <th class="text-right">Payment Count</th>
                    <th class="text-right">Percentage</th>
                </tr>
            </thead>
            <tbody>
                @foreach(array_slice($data['revenue_by_client'], 0, 15) as $client)
                <tr>
                    <td>{{ $client['client_name'] }}</td>
                    <td class="text-right">{{ getCurrencyAmount($client['total'], true) }}</td>
                    <td class="text-right">{{ number_format($client['count']) }}</td>
                    <td class="text-right">{{ number_format($client['percentage'], 1) }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <!-- Daily Revenue Summary -->
    @if(isset($data['daily_revenue']) && !empty($data['daily_revenue']))
    <div class="section-title">Daily Revenue Summary</div>
    <table>
        <thead>
            <tr>
                <th>Date</th>
                <th class="text-right">Revenue</th>
            </tr>
        </thead>
        <tbody>
            @foreach(array_slice($data['daily_revenue'], -10, 10, true) as $date => $revenue)
            <tr>
                <td>{{ \Carbon\Carbon::parse($date)->format('M j, Y') }}</td>
                <td class="text-right">{{ getCurrencyAmount($revenue, true) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <div class="footer">
        <p>This report was generated automatically by the Invoice Management System</p>
        <p>{{ config('app.name') }} - Revenue Analytics</p>
    </div>
</body>
</html>
