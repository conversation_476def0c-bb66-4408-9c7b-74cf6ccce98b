<?php

namespace App\Filament\Resources\ReportsResource\Pages;

use App\Filament\Resources\ReportsResource;
use App\Services\ReportingService;
use Filament\Resources\Pages\Page;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Actions\Action;
use Illuminate\Support\Facades\Response;

class CashFlowReport extends Page
{
    protected static string $resource = ReportsResource::class;
    
    protected static string $view = 'filament.resources.reports.pages.cash-flow-report';
    
    public ?array $data = [];
    
    public function mount(): void
    {
        $this->form->fill([
            'months' => 6,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('months')
                    ->label('Projection Period')
                    ->options([
                        3 => '3 Months',
                        6 => '6 Months',
                        12 => '12 Months',
                    ])
                    ->default(6)
                    ->required(),
            ])
            ->statePath('data')
            ->live();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('generate_report')
                ->label('Generate Report')
                ->icon('heroicon-o-arrow-path')
                ->action('generateReport'),
                
            Action::make('export_pdf')
                ->label('Export PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->action('exportPdf'),
                
            Action::make('export_excel')
                ->label('Export Excel')
                ->icon('heroicon-o-table-cells')
                ->action('exportExcel'),
                
            Action::make('back_to_reports')
                ->label('Back to Reports')
                ->icon('heroicon-o-arrow-left')
                ->url(fn () => ReportsResource::getUrl('index'))
                ->color('gray'),
        ];
    }

    public function generateReport(): void
    {
        $this->validate();
        
        $months = $this->data['months'] ?? 6;
        
        $reportingService = app(ReportingService::class);
        $this->reportData = $reportingService->getCashFlowProjections($months);
        
        $this->dispatch('report-generated');
    }

    public function exportPdf()
    {
        $this->generateReport();

        $pdf = app('dompdf.wrapper');
        $pdf->loadView('reports.cash-flow-pdf', [
            'data' => $this->reportData,
            'filters' => $this->data,
        ]);

        return $pdf->download('cash-flow-report-' . now()->format('Y-m-d') . '.pdf');
    }

    public function exportExcel()
    {
        $this->generateReport();

        $csvData = $this->prepareCsvData();

        return Response::streamDownload(function () use ($csvData) {
            echo $csvData;
        }, 'cash-flow-report-' . now()->format('Y-m-d') . '.csv', [
            'Content-Type' => 'text/csv',
        ]);
    }

    protected function prepareCsvData(): string
    {
        $csv = "Cash Flow Projections Report\n";
        $csv .= "Generated: " . now()->format('Y-m-d H:i:s') . "\n";
        $csv .= "Projection Period: {$this->data['months']} months\n\n";
        
        $csv .= "Monthly Projections\n";
        $csv .= "Month,Expected Income,Expected Expenses,Net Cash Flow,Cumulative Cash Flow\n";
        
        foreach ($this->reportData['monthly_projections'] as $projection) {
            $csv .= "{$projection['month']},{$projection['expected_income']},{$projection['expected_expenses']},{$projection['net_cash_flow']},{$projection['cumulative_cash_flow']}\n";
        }
        
        return $csv;
    }

    protected function getViewData(): array
    {
        if (!isset($this->reportData)) {
            $this->generateReport();
        }
        
        return [
            'reportData' => $this->reportData ?? [],
            'filters' => $this->data,
        ];
    }

    public $reportData = [];
}
