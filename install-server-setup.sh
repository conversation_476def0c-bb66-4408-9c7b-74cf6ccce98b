#!/bin/bash

# 🔥 BEAST MODE: Laravel Invoice Management System - Server Setup Script
# This script prepares your server for the Laravel Invoice Management System

echo "🔥 BEAST MODE: Laravel Invoice Management System Setup"
echo "=================================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Detect OS
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if [ -f /etc/debian_version ]; then
        OS="debian"
        print_info "Detected Debian/Ubuntu system"
    elif [ -f /etc/redhat-release ]; then
        OS="redhat"
        print_info "Detected RedHat/CentOS system"
    else
        OS="linux"
        print_info "Detected generic Linux system"
    fi
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
    print_info "Detected macOS system"
else
    print_warning "Unknown OS type: $OSTYPE"
    OS="unknown"
fi

echo ""
echo "🔧 STEP 1: Checking PHP Installation"
echo "=================================="

# Check PHP version
if command -v php &> /dev/null; then
    PHP_VERSION=$(php -r "echo PHP_VERSION;")
    print_status "PHP is installed: $PHP_VERSION"
    
    # Check if PHP version is >= 8.1
    if php -r "exit(version_compare(PHP_VERSION, '8.1.0', '>=') ? 0 : 1);"; then
        print_status "PHP version is compatible (>= 8.1)"
    else
        print_error "PHP version must be 8.1 or higher. Current: $PHP_VERSION"
        exit 1
    fi
else
    print_error "PHP is not installed"
    exit 1
fi

echo ""
echo "🧩 STEP 2: Checking PHP Extensions"
echo "================================="

# Required PHP extensions
REQUIRED_EXTENSIONS=("pdo" "pdo_mysql" "mbstring" "openssl" "tokenizer" "xml" "ctype" "fileinfo" "bcmath" "curl" "gd" "zip" "json")

for ext in "${REQUIRED_EXTENSIONS[@]}"; do
    if php -m | grep -q "^$ext$"; then
        print_status "$ext extension is loaded"
    else
        print_error "$ext extension is missing"
        
        # Provide installation commands based on OS
        case $OS in
            "debian")
                print_info "Install with: sudo apt-get install php-$ext"
                ;;
            "redhat")
                print_info "Install with: sudo yum install php-$ext"
                ;;
            *)
                print_info "Please install the $ext PHP extension"
                ;;
        esac
    fi
done

echo ""
echo "📁 STEP 3: Setting Up Directory Permissions"
echo "=========================================="

# Check if we're in a Laravel project directory
if [ ! -f "artisan" ]; then
    print_error "Not in a Laravel project directory. Please run this script from your project root."
    exit 1
fi

# Create required directories
mkdir -p storage/framework/sessions
mkdir -p storage/framework/views
mkdir -p storage/framework/cache
mkdir -p storage/logs
mkdir -p bootstrap/cache

# Set permissions
chmod -R 755 storage
chmod -R 755 bootstrap/cache

print_status "Directory permissions set"

echo ""
echo "🔑 STEP 4: Environment Configuration"
echo "=================================="

# Copy .env.example to .env if it doesn't exist
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example .env
        print_status ".env file created from .env.example"
    else
        print_error ".env.example file not found"
        exit 1
    fi
else
    print_info ".env file already exists"
fi

echo ""
echo "📦 STEP 5: Installing Dependencies"
echo "================================="

# Check if Composer is installed
if command -v composer &> /dev/null; then
    print_status "Composer is installed"
    
    # Install dependencies
    print_info "Installing PHP dependencies..."
    composer install --no-dev --optimize-autoloader
    
    if [ $? -eq 0 ]; then
        print_status "Dependencies installed successfully"
    else
        print_error "Failed to install dependencies"
        exit 1
    fi
else
    print_error "Composer is not installed. Please install Composer first."
    exit 1
fi

echo ""
echo "🔐 STEP 6: Generating Application Key"
echo "==================================="

php artisan key:generate --force
print_status "Application key generated"

echo ""
echo "🧹 STEP 7: Clearing Caches"
echo "========================="

php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

print_status "Caches cleared"

echo ""
echo "🌐 STEP 8: Web Server Configuration"
echo "=================================="

print_info "Make sure your web server is configured to:"
print_info "1. Point document root to the 'public' directory"
print_info "2. Enable URL rewriting (mod_rewrite for Apache)"
print_info "3. Set appropriate PHP memory limit (256M recommended)"
print_info "4. Enable required PHP extensions"

echo ""
echo "🎉 SETUP COMPLETE!"
echo "=================="
print_status "Your server is now ready for Laravel Invoice Management System"
print_info "Next steps:"
print_info "1. Configure your web server to point to the 'public' directory"
print_info "2. Visit your domain/install to start the installation wizard"
print_info "3. Follow the installation wizard to complete setup"

echo ""
print_warning "IMPORTANT: Make sure to secure your server and application before going live!"
