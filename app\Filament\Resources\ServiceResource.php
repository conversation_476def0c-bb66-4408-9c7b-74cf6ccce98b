<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ServiceResource\Pages;
use App\Models\Service;
use App\Models\Category;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use App\AdminDashboardSidebarSorting;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

class ServiceResource extends Resource
{
    protected static ?string $model = Service::class;

    protected static ?string $navigationIcon = 'heroicon-o-wrench-screwdriver';

    protected static ?int $navigationSort = AdminDashboardSidebarSorting::PRODUCTS->value;

    protected static ?string $navigationGroup = 'Logistics';

    public static function getNavigationLabel(): string
    {
        return __('Services');
    }

    public static function getModelLabel(): string
    {
        return __('Service');
    }

    public static function getPluralLabel(): string
    {
        return __('Services');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Service Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label('Service Name')
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder('e.g., Express Delivery, Freight Transport'),
                                
                                Forms\Components\TextInput::make('code')
                                    ->label('Service Code')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->maxLength(10)
                                    ->placeholder('e.g., EXP-DEL, FRT-TRP')
                                    ->helperText('Unique identifier for this service'),
                            ]),

                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Select::make('category_id')
                                    ->label('Category')
                                    ->required()
                                    ->relationship('category', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->createOptionForm([
                                        Forms\Components\TextInput::make('name')
                                            ->required()
                                            ->maxLength(255),
                                    ]),

                                Forms\Components\TextInput::make('unit_price')
                                    ->label('Base Price')
                                    ->required()
                                    ->numeric()
                                    ->prefix('$')
                                    ->minValue(0)
                                    ->step(0.01),

                                Forms\Components\Select::make('unit_type')
                                    ->label('Pricing Unit')
                                    ->required()
                                    ->options(Service::UNIT_TYPES)
                                    ->searchable()
                                    ->native(false),
                            ]),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active Service')
                            ->helperText('Only active services can be added to invoices')
                            ->default(true),

                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->rows(3)
                            ->placeholder('Describe what this service includes...'),

                        SpatieMediaLibraryFileUpload::make('service_image')
                            ->label('Service Image')
                            ->disk(config('app.media_disk'))
                            ->collection(Service::IMAGE)
                            ->image()
                            ->imageEditor()
                            ->imageCropAspectRatio('16:9')
                            ->helperText('Upload an image to represent this service'),
                    ]),

                Forms\Components\Section::make('Tiered Pricing (Optional)')
                    ->schema([
                        Forms\Components\Repeater::make('pricing_tiers')
                            ->label('Pricing Tiers')
                            ->schema([
                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\TextInput::make('min_quantity')
                                            ->label('Min Quantity')
                                            ->required()
                                            ->numeric()
                                            ->minValue(1),

                                        Forms\Components\TextInput::make('max_quantity')
                                            ->label('Max Quantity')
                                            ->numeric()
                                            ->minValue(1)
                                            ->helperText('Leave empty for unlimited'),

                                        Forms\Components\TextInput::make('price')
                                            ->label('Price per Unit')
                                            ->required()
                                            ->numeric()
                                            ->prefix('$')
                                            ->minValue(0)
                                            ->step(0.01),
                                    ]),
                            ])
                            ->collapsible()
                            ->collapsed()
                            ->addActionLabel('Add Pricing Tier')
                            ->helperText('Set different prices based on quantity ranges'),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\SpatieMediaLibraryImageColumn::make('service_image')
                    ->collection(Service::IMAGE)
                    ->label('')
                    ->circular()
                    ->size(50)
                    ->defaultImageUrl(fn() => asset('images/default-service.jpg')),

                Tables\Columns\TextColumn::make('name')
                    ->label('Service Name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('code')
                    ->label('Code')
                    ->searchable()
                    ->sortable()
                    ->fontFamily('mono')
                    ->color('gray'),

                Tables\Columns\TextColumn::make('category.name')
                    ->label('Category')
                    ->sortable()
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('unit_price')
                    ->label('Base Price')
                    ->money('USD')
                    ->sortable(),

                Tables\Columns\TextColumn::make('unit_type')
                    ->label('Unit')
                    ->formatStateUsing(fn (string $state): string => Service::UNIT_TYPES[$state] ?? $state)
                    ->badge()
                    ->color('success'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category_id')
                    ->label('Category')
                    ->relationship('category', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('unit_type')
                    ->label('Unit Type')
                    ->options(Service::UNIT_TYPES),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status')
                    ->placeholder('All services')
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate Selected')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->action(fn ($records) => $records->each->update(['is_active' => true])),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate Selected')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->action(fn ($records) => $records->each->update(['is_active' => false])),
                ]),
            ])
            ->defaultSort('name');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListServices::route('/'),
            'create' => Pages\CreateService::route('/create'),
            'view' => Pages\ViewService::route('/{record}'),
            'edit' => Pages\EditService::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['category']);
    }
}
