<?php

namespace App\Console\Commands;

use App\Models\Role;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Hash;

class SetupInstallCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'setup:install
                            {--force : Force installation even if already installed}
                            {--db-host= : Database host}
                            {--db-port= : Database port}
                            {--db-name= : Database name}
                            {--db-user= : Database username}
                            {--db-pass= : Database password}
                            {--admin-name= : Admin name}
                            {--admin-email= : Admin email}
                            {--admin-password= : Admin password}
                            {--app-name= : Application name}
                            {--skip-mail : Skip mail configuration}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Install the Laravel Invoice Management System via command line';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Laravel Invoice Management System - CLI Installation');
        $this->info('================================================================');

        // Check if already installed
        if (file_exists(storage_path('installed')) && !$this->option('force')) {
            $this->error('Application is already installed. Use --force to reinstall.');
            return 1;
        }

        try {
            // Step 1: Check requirements
            $this->info('📋 Step 1: Checking system requirements...');
            if (!$this->checkRequirements()) {
                return 1;
            }

            // Step 2: Database configuration
            $this->info('🗄️  Step 2: Database configuration...');
            $db_host = $this->option('db-host') ?: $this->ask('Database host', 'localhost');
            $db_port = $this->option('db-port') ?: $this->ask('Database port', '3306');
            $db_name = $this->option('db-name') ?: $this->ask('Database name');
            $db_user = $this->option('db-user') ?: $this->ask('Database username');
            $db_pass = $this->option('db-pass') ?: $this->secret('Database password');

            $this->info('   Testing database connection...');

            try {
                $dsn = "mysql:host={$db_host};port={$db_port};dbname={$db_name}";
                new \PDO($dsn, $db_user, $db_pass);
                $this->info('   ✅ Database connection successful');
            } catch (\PDOException $e) {
                $this->error('   ❌ Database connection failed: ' . $e->getMessage());
                return 1;
            }

            // Step 3: Update environment file
            $this->info('📝 Step 3: Updating environment configuration...');
            $envPath = base_path('.env');
            $envContent = file_get_contents($envPath);

            $envContent = preg_replace('/^DB_HOST=.*/m', "DB_HOST={$db_host}", $envContent);
            $envContent = preg_replace('/^DB_PORT=.*/m', "DB_PORT={$db_port}", $envContent);
            $envContent = preg_replace('/^DB_DATABASE=.*/m', "DB_DATABASE={$db_name}", $envContent);
            $envContent = preg_replace('/^DB_USERNAME=.*/m', "DB_USERNAME={$db_user}", $envContent);
            $envContent = preg_replace('/^DB_PASSWORD=.*/m', "DB_PASSWORD={$db_pass}", $envContent);

            // Application configuration
            $app_name = $this->option('app-name') ?: $this->ask('Application name', 'Laravel Invoice System');
            $envContent = preg_replace('/^APP_NAME=.*/m', "APP_NAME=\"{$app_name}\"", $envContent);

            // Mail configuration (optional)
            if (!$this->option('skip-mail')) {
                $this->info('📧 Step 4: Mail configuration (optional - press Enter to skip)...');
                $mail_host = $this->ask('Mail host (optional)');

                if ($mail_host) {
                    $mail_port = $this->ask('Mail port', '587');
                    $mail_user = $this->ask('Mail username');
                    $mail_pass = $this->secret('Mail password');
                    $mail_encryption = $this->choice('Mail encryption', ['tls', 'ssl', 'none'], 'tls');
                    $mail_from = $this->ask('Mail from address');

                    $envContent = preg_replace('/^MAIL_MAILER=.*/m', "MAIL_MAILER=smtp", $envContent);
                    $envContent = preg_replace('/^MAIL_HOST=.*/m', "MAIL_HOST={$mail_host}", $envContent);
                    $envContent = preg_replace('/^MAIL_PORT=.*/m', "MAIL_PORT={$mail_port}", $envContent);
                    $envContent = preg_replace('/^MAIL_USERNAME=.*/m', "MAIL_USERNAME={$mail_user}", $envContent);
                    $envContent = preg_replace('/^MAIL_PASSWORD=.*/m', "MAIL_PASSWORD={$mail_pass}", $envContent);
                    $envContent = preg_replace('/^MAIL_ENCRYPTION=.*/m', "MAIL_ENCRYPTION={$mail_encryption}", $envContent);
                    $envContent = preg_replace('/^MAIL_FROM_ADDRESS=.*/m', "MAIL_FROM_ADDRESS={$mail_from}", $envContent);

                    $this->info('   ✅ Mail configuration saved');
                } else {
                    $this->info('   ⏭️  Mail configuration skipped');
                }
            }

            file_put_contents($envPath, $envContent);
            $this->info('   ✅ Environment configuration updated');

            // Step 5: Generate application key
            $this->info('🔑 Step 5: Generating application key...');
            Artisan::call('key:generate', ['--force' => true]);
            $this->info('   ✅ Application key generated');

            // Step 6: Clear caches
            $this->info('🧹 Step 6: Clearing caches...');
            Artisan::call('config:clear');
            Artisan::call('cache:clear');
            Artisan::call('view:clear');
            $this->info('   ✅ Caches cleared');

            // Step 7: Run migrations and seeders
            $this->info('🏗️  Step 7: Setting up database...');
            Artisan::call('migrate', ['--force' => true]);
            $this->info('   ✅ Database migrations completed');

            Artisan::call('db:seed', ['--force' => true]);
            $this->info('   ✅ Database seeders completed');

            // Step 8: Create admin user
            $this->info('👤 Step 8: Creating admin user...');
            $name = $this->option('admin-name') ?: $this->ask('Admin name');
            $email = $this->option('admin-email') ?: $this->ask('Admin email');
            $password = $this->option('admin-password') ?: $this->secret('Admin password');

            $user = User::create([
                'name' => $name,
                'email' => $email,
                'password' => Hash::make($password),
                'email_verified_at' => now(),
            ]);

            $user->assignRole(Role::ROLE_ADMIN);
            $this->info('   ✅ Admin account created');

            // Step 9: Finalize installation
            $this->info('✅ Step 9: Finalizing installation...');

            // Cache for production
            if (app()->environment('production')) {
                Artisan::call('config:cache');
                Artisan::call('route:cache');
                Artisan::call('view:cache');
                Artisan::call('event:cache');
            }

            // Mark as installed
            $installationData = [
                'installed_at' => now()->toISOString(),
                'version' => '1.0.0',
                'php_version' => phpversion(),
                'admin_email' => $email,
            ];
            file_put_contents(storage_path('installed'), json_encode($installationData, JSON_PRETTY_PRINT));

            $this->info('');
            $this->info('🎉 Installation completed successfully!');
            $this->info('');
            $this->info('📋 Installation Summary:');
            $this->info("   Application: {$app_name}");
            $this->info("   Database: {$db_name} on {$db_host}:{$db_port}");
            $this->info("   Admin Email: {$email}");
            $this->info('');
            $this->info('🔗 You can now access your application and login with the admin credentials.');

        return 0;

        } catch (\Exception $e) {
            $this->error('❌ Installation failed: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Check system requirements
     */
    private function checkRequirements()
    {
        $this->info('   Checking PHP version...');
        if (version_compare(phpversion(), '8.1', '<')) {
            $this->error('   ❌ PHP 8.1 or higher is required. Current: ' . phpversion());
            return false;
        }
        $this->info('   ✅ PHP version: ' . phpversion());

        $this->info('   Checking required extensions...');
        $requiredExtensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'tokenizer', 'xml', 'ctype', 'fileinfo', 'bcmath', 'curl'];

        foreach ($requiredExtensions as $extension) {
            if (!extension_loaded($extension)) {
                $this->error("   ❌ Missing required extension: {$extension}");
                return false;
            }
            $this->info("   ✅ {$extension}");
        }

        $this->info('   Checking file permissions...');
        $requiredPaths = ['storage', 'bootstrap/cache'];

        foreach ($requiredPaths as $path) {
            $fullPath = base_path($path);
            if (!is_writable($fullPath)) {
                $this->error("   ❌ Directory not writable: {$path}");
                return false;
            }
            $this->info("   ✅ {$path}");
        }

        return true;
    }
}
