@extends('install.layouts.master')

@section('title', 'Database Setup Tool')

@section('content')
<div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8" x-data="webMigration()">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-purple-600 to-purple-700 px-8 py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-database text-2xl text-white"></i>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-white">🔥 BEAST MODE: Database Setup Tool</h1>
                        <p class="text-purple-100 mt-1">Shared hosting compatible database migration and seeding</p>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="px-8 py-6">
                <!-- Shared Hosting Notice -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-500 mr-3 mt-0.5"></i>
                        <div>
                            <h3 class="font-medium text-blue-800 mb-2">🌐 Shared Hosting Compatibility</h3>
                            <p class="text-blue-700 text-sm mb-2">
                                This tool is designed for shared hosting environments where command-line access is restricted.
                                It runs database migrations and seeders directly through the web interface.
                            </p>
                            <ul class="text-blue-700 text-sm list-disc list-inside">
                                <li>No command-line access required</li>
                                <li>Works with GoDaddy, Bluehost, and other shared hosting providers</li>
                                <li>Automatic fallback if Artisan commands fail</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Database Status -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">📊 Database Status</h2>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex gap-4 mb-4">
                            <button @click="testConnection()"
                                    :disabled="loading"
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50">
                                <i class="fas fa-flask mr-2" :class="{'fa-spin': loading && currentOperation === 'test'}"></i>
                                🧪 Test Connection
                            </button>

                            <button @click="checkStatus()"
                                    :disabled="loading"
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
                                <i class="fas fa-sync-alt mr-2" :class="{'fa-spin': loading}"></i>
                                Check Database Status
                            </button>
                        </div>
                        
                        <div x-show="status" class="mt-4">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="bg-white p-4 rounded border">
                                    <h4 class="font-medium text-gray-900">Connection</h4>
                                    <p x-show="status && status.database_connected" class="text-green-600">✅ Connected</p>
                                    <p x-show="status && !status.database_connected" class="text-red-600">❌ Failed</p>
                                </div>
                                <div class="bg-white p-4 rounded border">
                                    <h4 class="font-medium text-gray-900">Tables</h4>
                                    <p x-text="status ? `${status.tables_exist.length} exist, ${status.tables_missing.length} missing` : 'Checking...'"></p>
                                </div>
                                <div class="bg-white p-4 rounded border">
                                    <h4 class="font-medium text-gray-900">Data</h4>
                                    <p x-text="status ? `${status.roles_count} roles, ${status.permissions_count} permissions` : 'Checking...'"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Migration Section -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">🗄️ Database Migrations</h2>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-600 text-sm mb-4">
                            Run database migrations to create all required tables for the invoice management system.
                        </p>
                        
                        <button @click="runMigrations()" 
                                :disabled="loading"
                                class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50">
                            <i class="fas fa-play mr-2" :class="{'fa-spin': loading && currentOperation === 'migrations'}"></i>
                            🚀 Run Migrations
                        </button>
                        
                        <div x-show="migrationResults.length > 0" class="mt-4">
                            <h4 class="font-medium text-gray-900 mb-2">Migration Results:</h4>
                            <div class="bg-gray-800 text-green-400 p-3 rounded text-sm font-mono max-h-64 overflow-y-auto">
                                <template x-for="result in migrationResults">
                                    <div x-text="result"></div>
                                </template>
                            </div>
                        </div>
                        
                        <div x-show="migrationErrors.length > 0" class="mt-4">
                            <h4 class="font-medium text-red-800 mb-2">Migration Errors:</h4>
                            <div class="bg-red-50 border border-red-200 rounded p-3">
                                <template x-for="error in migrationErrors">
                                    <div class="text-red-700 text-sm" x-text="error"></div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Seeding Section -->
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">🌱 Database Seeding</h2>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-600 text-sm mb-4">
                            Run database seeders to populate the database with essential roles, permissions, and initial data.
                        </p>
                        
                        <button @click="runSeeders()" 
                                :disabled="loading"
                                class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50">
                            <i class="fas fa-seedling mr-2" :class="{'fa-spin': loading && currentOperation === 'seeders'}"></i>
                            🌱 Run Seeders
                        </button>
                        
                        <div x-show="seederResults.length > 0" class="mt-4">
                            <h4 class="font-medium text-gray-900 mb-2">Seeder Results:</h4>
                            <div class="bg-gray-800 text-green-400 p-3 rounded text-sm font-mono max-h-64 overflow-y-auto">
                                <template x-for="result in seederResults">
                                    <div x-text="result"></div>
                                </template>
                            </div>
                        </div>
                        
                        <div x-show="seederErrors.length > 0" class="mt-4">
                            <h4 class="font-medium text-red-800 mb-2">Seeder Errors:</h4>
                            <div class="bg-red-50 border border-red-200 rounded p-3">
                                <template x-for="error in seederErrors">
                                    <div class="text-red-700 text-sm" x-text="error"></div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Success Message -->
                <div x-show="allComplete" class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 text-xl mr-3"></i>
                        <div>
                            <h3 class="font-medium text-green-800">🎉 Database Setup Complete!</h3>
                            <p class="text-green-700 text-sm mt-1">
                                Your database has been successfully set up. You can now continue with the admin account creation.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="flex items-center justify-between pt-6 border-t">
                    <a href="{{ route('install.database') }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Database Config
                    </a>
                    
                    <a href="{{ route('install.admin') }}" 
                       x-show="allComplete"
                       class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Continue to Admin Setup
                        <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function webMigration() {
    return {
        loading: false,
        currentOperation: '',
        status: null,
        migrationResults: [],
        migrationErrors: [],
        seederResults: [],
        seederErrors: [],
        
        get allComplete() {
            return this.migrationResults.length > 0 &&
                   this.seederResults.length > 0 &&
                   this.migrationErrors.length === 0 &&
                   this.seederErrors.length === 0;
        },

        async testConnection() {
            this.loading = true;
            this.currentOperation = 'test';

            try {
                console.log('🧪 BEAST MODE: Testing web migration connection');

                const response = await fetch('/install/web-migration/test', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': window.csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                console.log('📡 Test response status:', response.status);
                console.log('📡 Test response headers:', response.headers.get('content-type'));

                // Check if response is actually JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const textResponse = await response.text();
                    console.error('❌ Non-JSON test response:', textResponse.substring(0, 500));
                    alert('❌ Test failed: Server returned HTML instead of JSON\n\nThis indicates a routing or PHP error.\n\nCheck browser console for details.');
                    return;
                }

                const data = await response.json();
                console.log('📊 Test response data:', data);

                if (data.success) {
                    alert('✅ Connection test successful!\n\n' +
                          'PHP Version: ' + data.php_version + '\n' +
                          'Laravel Version: ' + data.laravel_version + '\n' +
                          'Database: ' + data.database_config.database);
                } else {
                    alert('❌ Connection test failed: ' + data.error);
                }

            } catch (error) {
                console.error('❌ Test request failed:', error);
                alert('❌ Connection test failed: ' + error.message + '\n\nCheck browser console for details.');
            } finally {
                this.loading = false;
                this.currentOperation = '';
            }
        },
        
        async checkStatus() {
            this.loading = true;
            try {
                console.log('🔍 BEAST MODE: Checking database status');

                const response = await fetch('/install/web-migration/status', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': window.csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                console.log('📡 Status response:', response.status);

                // Check if response is actually JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const textResponse = await response.text();
                    console.error('❌ Non-JSON status response:', textResponse.substring(0, 500));
                    alert('Server error: Received HTML instead of JSON. Check browser console for details.');
                    return;
                }

                const data = await response.json();
                console.log('📊 Status data:', data);

                if (data.success) {
                    this.status = data.status;
                } else {
                    console.error('❌ Status check failed:', data);
                    alert('Failed to check status: ' + (data.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('❌ Status request failed:', error);
                alert('Error checking status: ' + error.message + '. Check browser console for details.');
            } finally {
                this.loading = false;
            }
        },
        
        async runMigrations() {
            this.loading = true;
            this.currentOperation = 'migrations';
            this.migrationResults = [];
            this.migrationErrors = [];

            try {
                console.log('🚀 BEAST MODE: Starting migration request');

                const response = await fetch('/install/web-migration/migrations', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': window.csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                console.log('📡 Response status:', response.status);
                console.log('📡 Response headers:', response.headers.get('content-type'));

                // Check if response is actually JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const textResponse = await response.text();
                    console.error('❌ Non-JSON response received:', textResponse.substring(0, 500));

                    this.migrationErrors = [
                        '❌ Server returned HTML instead of JSON',
                        '🔍 This usually indicates a PHP error or redirect',
                        '📋 Response preview: ' + textResponse.substring(0, 200) + '...',
                        '🔧 Check browser console for full response'
                    ];
                    return;
                }

                const data = await response.json();
                console.log('📊 Migration response data:', data);

                this.migrationResults = data.results || [];
                this.migrationErrors = data.errors || [];

                // Show debug info if available
                if (data.debug) {
                    this.migrationResults.push('🔍 Debug info: ' + JSON.stringify(data.debug));
                }

                if (data.success) {
                    await this.checkStatus();
                } else {
                    console.error('❌ Migration failed:', data);
                }
            } catch (error) {
                console.error('❌ Migration request failed:', error);
                this.migrationErrors = [
                    '❌ Error running migrations: ' + error.message,
                    '🔍 Check browser console for detailed error information',
                    '🔧 This might be a network issue or server error'
                ];
            } finally {
                this.loading = false;
                this.currentOperation = '';
            }
        },
        
        async runSeeders() {
            this.loading = true;
            this.currentOperation = 'seeders';
            this.seederResults = [];
            this.seederErrors = [];

            try {
                console.log('🌱 BEAST MODE: Starting seeder request');

                const response = await fetch('/install/web-migration/seeders', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': window.csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                console.log('📡 Seeder response status:', response.status);

                // Check if response is actually JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const textResponse = await response.text();
                    console.error('❌ Non-JSON response received:', textResponse.substring(0, 500));

                    this.seederErrors = [
                        '❌ Server returned HTML instead of JSON',
                        '🔍 This usually indicates a PHP error or redirect',
                        '📋 Response preview: ' + textResponse.substring(0, 200) + '...',
                        '🔧 Check browser console for full response'
                    ];
                    return;
                }

                const data = await response.json();
                console.log('📊 Seeder response data:', data);

                this.seederResults = data.results || [];
                this.seederErrors = data.errors || [];

                // Show debug info if available
                if (data.debug) {
                    this.seederResults.push('🔍 Debug info: ' + JSON.stringify(data.debug));
                }

                if (data.success) {
                    await this.checkStatus();
                } else {
                    console.error('❌ Seeding failed:', data);
                }
            } catch (error) {
                console.error('❌ Seeder request failed:', error);
                this.seederErrors = [
                    '❌ Error running seeders: ' + error.message,
                    '🔍 Check browser console for detailed error information',
                    '🔧 This might be a network issue or server error'
                ];
            } finally {
                this.loading = false;
                this.currentOperation = '';
            }
        },
        
        init() {
            this.checkStatus();
        }
    }
}
</script>
@endsection
