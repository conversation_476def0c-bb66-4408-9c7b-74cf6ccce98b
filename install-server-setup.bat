@echo off
REM 🔥 BEAST MODE: Laravel Invoice Management System - Windows Setup Script
REM This script prepares your Windows server for the Laravel Invoice Management System

echo 🔥 BEAST MODE: Laravel Invoice Management System Setup
echo ==================================================
echo.

REM Check if PHP is installed
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP is not installed or not in PATH
    echo Please install PHP 8.1 or higher and add it to your PATH
    pause
    exit /b 1
)

echo ✅ PHP is installed
php -r "echo 'PHP Version: ' . PHP_VERSION . PHP_EOL;"

REM Check PHP version
php -r "exit(version_compare(PHP_VERSION, '8.1.0', '>=') ? 0 : 1);"
if %errorlevel% neq 0 (
    echo ❌ PHP version must be 8.1 or higher
    pause
    exit /b 1
)

echo ✅ PHP version is compatible

echo.
echo 🧩 Checking PHP Extensions
echo =========================

REM Check required extensions
set "extensions=pdo pdo_mysql mbstring openssl tokenizer xml ctype fileinfo bcmath curl gd zip json"

for %%e in (%extensions%) do (
    php -m | findstr /i "%%e" >nul
    if !errorlevel! equ 0 (
        echo ✅ %%e extension is loaded
    ) else (
        echo ❌ %%e extension is missing
        echo    For XAMPP: Edit php.ini and uncomment extension=%%e
    )
)

echo.
echo 📁 Setting Up Directory Permissions
echo ==================================

REM Check if we're in a Laravel project directory
if not exist "artisan" (
    echo ❌ Not in a Laravel project directory
    echo Please run this script from your project root
    pause
    exit /b 1
)

REM Create required directories
if not exist "storage\framework\sessions" mkdir "storage\framework\sessions"
if not exist "storage\framework\views" mkdir "storage\framework\views"
if not exist "storage\framework\cache" mkdir "storage\framework\cache"
if not exist "storage\logs" mkdir "storage\logs"
if not exist "bootstrap\cache" mkdir "bootstrap\cache"

echo ✅ Directories created

echo.
echo 🔑 Environment Configuration
echo ===========================

REM Copy .env.example to .env if it doesn't exist
if not exist ".env" (
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo ✅ .env file created from .env.example
    ) else (
        echo ❌ .env.example file not found
        pause
        exit /b 1
    )
) else (
    echo ℹ️ .env file already exists
)

echo.
echo 📦 Installing Dependencies
echo =========================

REM Check if Composer is installed
composer --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Composer is not installed
    echo Please install Composer from https://getcomposer.org/
    pause
    exit /b 1
)

echo ✅ Composer is installed

REM Install dependencies
echo Installing PHP dependencies...
composer install --no-dev --optimize-autoloader

if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully

echo.
echo 🔐 Generating Application Key
echo ============================

php artisan key:generate --force
echo ✅ Application key generated

echo.
echo 🧹 Clearing Caches
echo ==================

php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

echo ✅ Caches cleared

echo.
echo 🌐 Web Server Configuration
echo ===========================

echo ℹ️ Make sure your web server is configured to:
echo 1. Point document root to the 'public' directory
echo 2. Enable URL rewriting
echo 3. Set appropriate PHP memory limit (256M recommended)
echo 4. Enable required PHP extensions

echo.
echo 🎉 SETUP COMPLETE!
echo =================
echo ✅ Your server is now ready for Laravel Invoice Management System
echo.
echo ℹ️ Next steps:
echo 1. Configure your web server to point to the 'public' directory
echo 2. Visit your domain/install to start the installation wizard
echo 3. Follow the installation wizard to complete setup
echo.
echo ⚠️ IMPORTANT: Make sure to secure your server and application before going live!

pause
