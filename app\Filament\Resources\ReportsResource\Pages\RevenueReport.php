<?php

namespace App\Filament\Resources\ReportsResource\Pages;

use App\Filament\Resources\ReportsResource;
use App\Services\ReportingService;
use App\Models\Currency;
use Filament\Resources\Pages\Page;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Actions\Action;
use Carbon\Carbon;
use Illuminate\Support\Facades\Response;

class RevenueReport extends Page
{
    protected static string $resource = ReportsResource::class;
    
    protected static string $view = 'filament.resources.reports.pages.revenue-report';
    
    public ?array $data = [];
    
    public function mount(): void
    {
        $this->form->fill([
            'start_date' => now()->startOfMonth()->format('Y-m-d'),
            'end_date' => now()->endOfMonth()->format('Y-m-d'),
            'currency_id' => null,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('start_date')
                    ->label('Start Date')
                    ->required()
                    ->default(now()->startOfMonth()),
                    
                DatePicker::make('end_date')
                    ->label('End Date')
                    ->required()
                    ->default(now()->endOfMonth()),
                    
                Select::make('currency_id')
                    ->label('Currency Filter')
                    ->options(Currency::pluck('name', 'id'))
                    ->placeholder('All Currencies')
                    ->searchable(),
            ])
            ->statePath('data')
            ->live();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('generate_report')
                ->label('Generate Report')
                ->icon('heroicon-o-arrow-path')
                ->action('generateReport'),
                
            Action::make('export_pdf')
                ->label('Export PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->action('exportPdf'),
                
            Action::make('export_excel')
                ->label('Export Excel')
                ->icon('heroicon-o-table-cells')
                ->action('exportExcel'),
                
            Action::make('back_to_reports')
                ->label('Back to Reports')
                ->icon('heroicon-o-arrow-left')
                ->url(fn () => ReportsResource::getUrl('index'))
                ->color('gray'),
        ];
    }

    public function generateReport(): void
    {
        $this->validate();
        
        $startDate = Carbon::parse($this->data['start_date']);
        $endDate = Carbon::parse($this->data['end_date']);
        $currencyId = $this->data['currency_id'] ?? null;
        
        $reportingService = app(ReportingService::class);
        $this->reportData = $reportingService->getRevenueAnalytics($startDate, $endDate, $currencyId);
        $this->categoryData = $reportingService->getRevenueByCategory($startDate, $endDate);
        
        $this->dispatch('report-generated');
    }

    public function exportPdf()
    {
        $this->generateReport();

        $pdf = app('dompdf.wrapper');
        $pdf->loadView('reports.revenue-pdf', [
            'data' => $this->reportData,
            'categoryData' => $this->categoryData,
            'filters' => $this->data,
        ]);

        return $pdf->download('revenue-report-' . now()->format('Y-m-d') . '.pdf');
    }

    public function exportExcel()
    {
        $this->generateReport();

        // Implementation would use Laravel Excel package
        // For now, return CSV format
        $csvData = $this->prepareCsvData();

        return Response::streamDownload(function () use ($csvData) {
            echo $csvData;
        }, 'revenue-report-' . now()->format('Y-m-d') . '.csv', [
            'Content-Type' => 'text/csv',
        ]);
    }

    protected function prepareCsvData(): string
    {
        $csv = "Revenue Report\n";
        $csv .= "Generated: " . now()->format('Y-m-d H:i:s') . "\n";
        $csv .= "Period: {$this->data['start_date']} to {$this->data['end_date']}\n\n";
        
        $csv .= "Summary\n";
        $csv .= "Total Revenue,{$this->reportData['total_revenue']}\n";
        $csv .= "Payment Count,{$this->reportData['payment_count']}\n";
        $csv .= "Average Payment,{$this->reportData['average_payment']}\n\n";
        
        $csv .= "Revenue by Client\n";
        $csv .= "Client Name,Total Revenue,Payment Count\n";
        foreach ($this->reportData['revenue_by_client'] as $client) {
            $csv .= "\"{$client['client_name']}\",{$client['total']},{$client['count']}\n";
        }
        
        return $csv;
    }

    protected function getViewData(): array
    {
        if (!isset($this->reportData)) {
            $this->generateReport();
        }
        
        return [
            'reportData' => $this->reportData ?? [],
            'categoryData' => $this->categoryData ?? [],
            'filters' => $this->data,
        ];
    }

    public $reportData = [];
    public $categoryData = [];
}
