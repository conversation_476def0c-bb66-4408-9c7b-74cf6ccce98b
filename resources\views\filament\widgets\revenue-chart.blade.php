<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center space-x-2">
                <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <span>Revenue Analytics - {{ $period }}</span>
            </div>
        </x-slot>
        
        <x-slot name="headerEnd">
            <div class="flex items-center space-x-4 text-sm text-gray-500">
                @if($hasData)
                    <div class="flex items-center space-x-1">
                        <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                        <span>Live Data</span>
                    </div>
                @else
                    <div class="flex items-center space-x-1">
                        <span class="w-2 h-2 bg-gray-400 rounded-full"></span>
                        <span>No Data</span>
                    </div>
                @endif
            </div>
        </x-slot>

        <div class="space-y-4">
            <!-- KPI Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-blue-600 dark:text-blue-400">Total Revenue</p>
                            <p class="text-2xl font-bold text-blue-900 dark:text-blue-100">
                                {{ getCurrencyAmount($totalRevenue, true) }}
                            </p>
                        </div>
                        <div class="p-2 bg-blue-500 rounded-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-green-600 dark:text-green-400">Payments</p>
                            <p class="text-2xl font-bold text-green-900 dark:text-green-100">
                                {{ number_format($paymentCount) }}
                            </p>
                        </div>
                        <div class="p-2 bg-green-500 rounded-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-purple-600 dark:text-purple-400">Average Payment</p>
                            <p class="text-2xl font-bold text-purple-900 dark:text-purple-100">
                                {{ getCurrencyAmount($averagePayment, true) }}
                            </p>
                        </div>
                        <div class="p-2 bg-purple-500 rounded-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chart Container -->
            <div class="relative bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                @if($hasData)
                    <div class="relative" style="height: 400px;">
                        <canvas id="revenueChart" class="w-full h-full"></canvas>
                    </div>
                @else
                    <div class="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400">
                        <svg class="w-16 h-16 mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <h3 class="text-lg font-medium mb-2">No Revenue Data Available</h3>
                        <p class="text-center max-w-md">
                            Start creating invoices and receiving payments to see your revenue analytics here.
                        </p>
                        @if(isset($error))
                            <p class="text-red-500 text-sm mt-2">Error: {{ $error }}</p>
                        @endif
                    </div>
                @endif
            </div>

            <!-- Chart Legend and Controls -->
            @if($hasData)
                <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span>Daily Revenue Trend</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="refreshChart()" class="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Refresh
                        </button>
                    </div>
                </div>
            @endif
        </div>
    </x-filament::section>

    @if($hasData)
        @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                initializeRevenueChart();
            });

            let revenueChart;

            function initializeRevenueChart() {
                const ctx = document.getElementById('revenueChart');
                if (!ctx) return;

                const chartConfig = {!! $chartConfig !!};
                
                // Destroy existing chart if it exists
                if (revenueChart) {
                    revenueChart.destroy();
                }
                
                try {
                    revenueChart = new Chart(ctx, chartConfig);
                } catch (error) {
                    console.error('Error initializing revenue chart:', error);
                    showChartError();
                }
            }

            function refreshChart() {
                // Trigger a page refresh or use Livewire to refresh the widget
                window.location.reload();
            }

            function showChartError() {
                const canvas = document.getElementById('revenueChart');
                if (canvas) {
                    const parent = canvas.parentElement;
                    parent.innerHTML = `
                        <div class="flex items-center justify-center h-64 text-red-500">
                            <div class="text-center">
                                <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                <h3 class="text-lg font-medium mb-2">Chart Error</h3>
                                <p>Unable to load the revenue chart. Please refresh the page.</p>
                            </div>
                        </div>
                    `;
                }
            }

            // Handle responsive chart resizing
            window.addEventListener('resize', function() {
                if (revenueChart) {
                    revenueChart.resize();
                }
            });
        </script>
        @endpush
    @endif
</x-filament-widgets::widget>
