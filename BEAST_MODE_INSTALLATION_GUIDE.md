# 🔥 BEAST MODE: Laravel Invoice Management System Installation Guide

## 🚀 **COMPLETE SERVER SETUP & INSTALLATION COMMANDS**

This guide provides **BULLETPROOF** installation instructions for the Laravel Invoice Management System with **ZERO TOLERANCE** for errors!

---

## 📋 **PRE-INSTALLATION REQUIREMENTS**

### **Minimum Server Requirements:**
- **PHP**: 8.1 or higher
- **Database**: MySQL 5.7+ or PostgreSQL 10+
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **Memory**: 256MB RAM minimum
- **Storage**: 1GB free space

### **Required PHP Extensions:**
- pdo, pdo_mysql, mbstring, openssl, tokenizer
- xml, ctype, fileinfo, bcmath, curl, gd, json

---

## 🔧 **STEP 1: SERVER PREPARATION**

### **🐧 For Linux (Ubuntu/Debian):**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install PHP 8.1 and required extensions
sudo apt install php8.1 php8.1-cli php8.1-fpm php8.1-mysql php8.1-xml php8.1-curl php8.1-gd php8.1-mbstring php8.1-zip php8.1-bcmath php8.1-tokenizer php8.1-fileinfo php8.1-ctype php8.1-openssl -y

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Install MySQL
sudo apt install mysql-server -y
sudo mysql_secure_installation

# Install Apache
sudo apt install apache2 -y
sudo a2enmod rewrite
sudo systemctl restart apache2
```

### **🎩 For CentOS/RHEL:**
```bash
# Install EPEL repository
sudo yum install epel-release -y

# Install Remi repository for PHP 8.1
sudo yum install https://rpms.remirepo.net/enterprise/remi-release-8.rpm -y
sudo yum module enable php:remi-8.1 -y

# Install PHP and extensions
sudo yum install php php-cli php-fpm php-mysql php-xml php-curl php-gd php-mbstring php-zip php-bcmath php-tokenizer php-fileinfo php-ctype php-openssl -y

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Install MySQL
sudo yum install mysql-server -y
sudo systemctl start mysqld
sudo mysql_secure_installation
```

### **🪟 For Windows (XAMPP):**
```batch
REM Download and install XAMPP from https://www.apachefriends.org/
REM 1. Download XAMPP with PHP 8.1+
REM 2. Install XAMPP to C:\xampp
REM 3. Start Apache and MySQL from XAMPP Control Panel
REM 4. Download Composer from https://getcomposer.org/
```

---

## 🔍 **STEP 2: VERIFY REQUIREMENTS**

### **Run Requirements Checker:**
```bash
# Navigate to your project directory
cd /path/to/your/laravel-invoice-system

# Run the requirements checker
php check-requirements.php
```

**Expected Output:**
```
🔥 BEAST MODE: Server Requirements Checker
==========================================

📋 PHP VERSION CHECK:
✅ PHP Version: 8.1.x (Required: >= 8.1.0)

🧩 PHP EXTENSIONS CHECK:
✅ pdo: Loaded (Required for database connections)
✅ pdo_mysql: Loaded (Required for MySQL database connections)
... (all extensions should show ✅)

🎉 CONGRATULATIONS!
Your server meets all requirements!
```

---

## ⚙️ **STEP 3: AUTOMATED SETUP**

### **🐧 For Linux/Mac:**
```bash
# Make setup script executable
chmod +x install-server-setup.sh

# Run the setup script
./install-server-setup.sh
```

### **🪟 For Windows:**
```batch
REM Run the Windows setup script
install-server-setup.bat
```

---

## 🗄️ **STEP 4: DATABASE PREPARATION**

### **Create Database:**
```sql
-- Login to MySQL
mysql -u root -p

-- Create database
CREATE DATABASE invoices CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user (optional, for security)
CREATE USER 'invoiceuser'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON invoices.* TO 'invoiceuser'@'localhost';
FLUSH PRIVILEGES;

-- Exit MySQL
EXIT;
```

---

## 🌐 **STEP 5: WEB SERVER CONFIGURATION**

### **Apache Configuration:**
```apache
# Create virtual host file: /etc/apache2/sites-available/invoices.conf
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/laravel-invoice-system/public
    
    <Directory /path/to/laravel-invoice-system/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/invoices_error.log
    CustomLog ${APACHE_LOG_DIR}/invoices_access.log combined
</VirtualHost>

# Enable the site
sudo a2ensite invoices.conf
sudo systemctl reload apache2
```

### **Nginx Configuration:**
```nginx
# Create server block: /etc/nginx/sites-available/invoices
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/laravel-invoice-system/public;
    
    index index.php index.html index.htm;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    location ~ /\.ht {
        deny all;
    }
}

# Enable the site
sudo ln -s /etc/nginx/sites-available/invoices /etc/nginx/sites-enabled/
sudo systemctl reload nginx
```

---

## 🚀 **STEP 6: START INSTALLATION WIZARD**

### **Access the Installation Wizard:**
```
http://your-domain.com/install
```

### **Installation Steps:**
1. **Welcome Page** - Review system information
2. **Server Requirements** - Verify all requirements are met
3. **Folder Permissions** - Check directory permissions
4. **Database Setup** - Configure database connection
5. **Mail Configuration** - Setup email settings (optional)
6. **Admin Account** - Create administrator account
7. **Company Branding** - Configure company details
8. **Installation Summary** - Review configuration
9. **Success** - Installation complete!

---

## 🔧 **TROUBLESHOOTING COMMANDS**

### **Fix Common Issues:**

#### **419 Page Expired Error:**
```bash
# Clear all caches
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

# Fix session permissions
chmod -R 755 storage/framework/sessions
chown -R www-data:www-data storage/framework/sessions

# Restart web server
sudo systemctl restart apache2  # or nginx
```

#### **Database Connection Issues:**
```bash
# Test database connection
mysql -u root -p -e "SELECT 1;"

# Check MySQL service
sudo systemctl status mysql

# Restart MySQL
sudo systemctl restart mysql
```

#### **Permission Issues:**
```bash
# Fix Laravel permissions
sudo chown -R www-data:www-data /path/to/laravel-invoice-system
sudo chmod -R 755 /path/to/laravel-invoice-system
sudo chmod -R 775 /path/to/laravel-invoice-system/storage
sudo chmod -R 775 /path/to/laravel-invoice-system/bootstrap/cache
```

#### **PHP Extension Issues:**
```bash
# Check loaded extensions
php -m | grep -i mysql

# Install missing extensions (Ubuntu)
sudo apt install php8.1-mysql php8.1-mbstring php8.1-xml

# Restart web server
sudo systemctl restart apache2
```

---

## 🎯 **COMMAND-LINE INSTALLATION (ADVANCED)**

### **Silent Installation:**
```bash
php artisan setup:install \
    --db-host=localhost \
    --db-port=3306 \
    --db-name=invoices \
    --db-user=root \
    --db-pass=password \
    --admin-name="Admin User" \
    --admin-email=<EMAIL> \
    --admin-password=secure_password \
    --app-name="My Invoice System" \
    --force
```

---

## 🔒 **POST-INSTALLATION SECURITY**

### **Secure Your Installation:**
```bash
# Set proper file permissions
find /path/to/laravel-invoice-system -type f -exec chmod 644 {} \;
find /path/to/laravel-invoice-system -type d -exec chmod 755 {} \;
chmod -R 775 storage bootstrap/cache

# Secure .env file
chmod 600 .env

# Remove installation files (optional)
rm -f check-requirements.php
rm -f install-server-setup.sh
rm -f install-server-setup.bat

# Enable HTTPS (recommended)
sudo certbot --apache -d your-domain.com
```

---

## 🎉 **VERIFICATION**

### **Test Your Installation:**
```bash
# Check application status
php artisan about

# Test database connection
php artisan tinker
>>> DB::connection()->getPdo();

# Access admin panel
# Visit: http://your-domain.com/admin
```

---

## 🆘 **EMERGENCY RECOVERY**

### **Reset Installation:**
```bash
# Remove installation lock
rm -f storage/installed

# Reset database
php artisan migrate:fresh --seed

# Clear all caches
php artisan optimize:clear

# Restart installation
# Visit: http://your-domain.com/install
```

---

**🔥 BEAST MODE INSTALLATION COMPLETE!** 

Your Laravel Invoice Management System is now ready for production use! 🚀
