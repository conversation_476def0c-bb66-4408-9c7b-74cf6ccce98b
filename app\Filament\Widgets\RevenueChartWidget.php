<?php

namespace App\Filament\Widgets;

use App\Services\ChartService;
use App\Services\ReportingService;
use Filament\Widgets\Widget;
use Carbon\Carbon;

class RevenueChartWidget extends Widget
{
    protected static string $view = 'filament.widgets.revenue-chart';
    
    protected int | string | array $columnSpan = 'full';
    
    protected static ?int $sort = 1;
    
    public function getViewData(): array
    {
        $reportingService = app(ReportingService::class);
        $chartService = app(ChartService::class);
        
        // Get current month data
        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();
        
        try {
            $revenueData = $reportingService->getRevenueAnalytics($startDate, $endDate);
            $chartConfig = $chartService->generateRevenueChart($revenueData);
            
            return [
                'chartConfig' => json_encode($chartConfig),
                'totalRevenue' => $revenueData['total_revenue'] ?? 0,
                'paymentCount' => $revenueData['payment_count'] ?? 0,
                'averagePayment' => $revenueData['average_payment'] ?? 0,
                'period' => $startDate->format('M Y'),
                'hasData' => !empty($revenueData['daily_revenue'])
            ];
        } catch (\Exception $e) {
            // Fallback data if there's an error
            return [
                'chartConfig' => json_encode($this->getFallbackChartConfig()),
                'totalRevenue' => 0,
                'paymentCount' => 0,
                'averagePayment' => 0,
                'period' => $startDate->format('M Y'),
                'hasData' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function getFallbackChartConfig(): array
    {
        return [
            'type' => 'line',
            'data' => [
                'labels' => ['No Data'],
                'datasets' => [
                    [
                        'label' => 'Daily Revenue',
                        'data' => [0],
                        'borderColor' => 'rgb(156, 163, 175)',
                        'backgroundColor' => 'rgba(156, 163, 175, 0.1)',
                        'tension' => 0.4
                    ]
                ]
            ],
            'options' => [
                'responsive' => true,
                'maintainAspectRatio' => false,
                'plugins' => [
                    'legend' => [
                        'display' => false
                    ]
                ]
            ]
        ];
    }
}
