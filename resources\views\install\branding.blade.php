@extends('install.layouts.master')

@section('title', 'Company Branding')

@section('content')
<div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-palette text-2xl text-white"></i>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-white">Company Branding</h1>
                        <p class="text-blue-100 mt-1">Customize your application with company details</p>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="px-8 py-6">
                <form action="{{ route('install.branding') }}" method="POST" enctype="multipart/form-data" class="space-y-6" x-data="brandingForm()">
                    @csrf

                    <!-- Company Information -->
                    <div class="space-y-6">
                        <div>
                            <label for="company_name" class="block text-sm font-medium text-gray-700">Company Name</label>
                            <input type="text"
                                   id="company_name"
                                   name="company_name"
                                   value="{{ old('company_name') }}"
                                   required
                                   x-model="companyName"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="Your Company Name">
                            @error('company_name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="company_logo" class="block text-sm font-medium text-gray-700">Company Logo</label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors duration-200">
                                <div class="space-y-1 text-center">
                                    <div x-show="!logoPreview">
                                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400"></i>
                                        <div class="flex text-sm text-gray-600">
                                            <label for="company_logo" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                                <span>Upload a logo</span>
                                                <input id="company_logo"
                                                       name="company_logo"
                                                       type="file"
                                                       accept="image/*"
                                                       @change="handleLogoUpload"
                                                       class="sr-only">
                                            </label>
                                            <p class="pl-1">or drag and drop</p>
                                        </div>
                                        <p class="text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                                    </div>

                                    <div x-show="logoPreview" class="relative">
                                        <img :src="logoPreview" class="mx-auto h-32 w-auto rounded-lg shadow-md">
                                        <button type="button"
                                                @click="removeLogo"
                                                class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        <div class="mt-2">
                                            <label for="company_logo" class="cursor-pointer text-sm text-blue-600 hover:text-blue-500">
                                                Change logo
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @error('company_logo')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="company_currency" class="block text-sm font-medium text-gray-700">Default Currency</label>
                            <select id="company_currency"
                                    name="company_currency"
                                    required
                                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                <option value="">Select Currency</option>
                                <option value="USD" {{ old('company_currency') === 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
                                <option value="EUR" {{ old('company_currency') === 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
                                <option value="GBP" {{ old('company_currency') === 'GBP' ? 'selected' : '' }}>GBP - British Pound</option>
                                <option value="CAD" {{ old('company_currency') === 'CAD' ? 'selected' : '' }}>CAD - Canadian Dollar</option>
                                <option value="AUD" {{ old('company_currency') === 'AUD' ? 'selected' : '' }}>AUD - Australian Dollar</option>
                                <option value="JPY" {{ old('company_currency') === 'JPY' ? 'selected' : '' }}>JPY - Japanese Yen</option>
                                <option value="INR" {{ old('company_currency') === 'INR' ? 'selected' : '' }}>INR - Indian Rupee</option>
                                <option value="NGN" {{ old('company_currency') === 'NGN' ? 'selected' : '' }}>NGN - Nigerian Naira</option>
                            </select>
                            @error('company_currency')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="company_phone" class="block text-sm font-medium text-gray-700">Phone Number (Optional)</label>
                                <input type="tel"
                                       id="company_phone"
                                       name="company_phone"
                                       value="{{ old('company_phone') }}"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                       placeholder="+****************">
                                @error('company_phone')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="company_email" class="block text-sm font-medium text-gray-700">Company Email (Optional)</label>
                                <input type="email"
                                       id="company_email"
                                       name="company_email"
                                       value="{{ old('company_email') }}"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                       placeholder="<EMAIL>">
                                @error('company_email')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <label for="company_address" class="block text-sm font-medium text-gray-700">Company Address (Optional)</label>
                            <textarea id="company_address"
                                      name="company_address"
                                      rows="3"
                                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                      placeholder="123 Business Street, City, State, ZIP Code">{{ old('company_address') }}</textarea>
                            @error('company_address')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Preview Section -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="font-medium text-gray-900 mb-4">Preview</h3>
                        <div class="bg-white rounded-lg p-4 border">
                            <div class="flex items-center space-x-4">
                                <div class="flex-shrink-0">
                                    <div x-show="logoPreview" class="w-16 h-16 rounded-lg overflow-hidden">
                                        <img :src="logoPreview" class="w-full h-full object-cover">
                                    </div>
                                    <div x-show="!logoPreview" class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-building text-gray-400 text-xl"></i>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900" x-text="companyName || 'Your Company Name'"></h4>
                                    <p class="text-sm text-gray-600">Invoice Management System</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer Buttons -->
                    <div class="flex items-center justify-between pt-6 border-t">
                        <a href="{{ route('install.admin') }}"
                           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back
                        </a>

                        <button type="submit"
                                class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Save & Continue
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function brandingForm() {
    return {
        logoPreview: null,
        companyName: '{{ old("company_name") }}',

        handleLogoUpload(event) {
            const file = event.target.files[0];
            if (file) {
                // Validate file size (2MB)
                if (file.size > 2 * 1024 * 1024) {
                    alert('File size must be less than 2MB');
                    event.target.value = '';
                    return;
                }

                // Validate file type
                if (!file.type.startsWith('image/')) {
                    alert('Please select an image file');
                    event.target.value = '';
                    return;
                }

                const reader = new FileReader();
                reader.onload = (e) => {
                    this.logoPreview = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        },

        removeLogo() {
            this.logoPreview = null;
            document.getElementById('company_logo').value = '';
        }
    }
}
</script>
@endpush
@endsection