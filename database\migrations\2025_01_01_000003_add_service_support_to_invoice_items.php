<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_items', function (Blueprint $table) {
            $table->foreignId('service_id')->nullable()->after('product_id')->constrained()->onDelete('cascade');
            $table->string('service_name')->nullable()->after('product_name');
            $table->enum('item_type', ['product', 'service'])->default('product')->after('service_name');
            
            // Make product_id nullable since we can have services instead
            $table->foreignId('product_id')->nullable()->change();
            
            $table->index(['item_type', 'service_id']);
            $table->index(['item_type', 'product_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_items', function (Blueprint $table) {
            $table->dropForeign(['service_id']);
            $table->dropIndex(['item_type', 'service_id']);
            $table->dropIndex(['item_type', 'product_id']);
            $table->dropColumn(['service_id', 'service_name', 'item_type']);
        });
    }
};
