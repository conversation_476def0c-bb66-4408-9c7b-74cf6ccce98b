<?php

namespace App\Http\Responses;

use App\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Filament\Http\Responses\Auth\Contracts\LoginResponse as LoginResponseContract;

class LoginResponse implements LoginResponseContract
{
    public function toResponse($request)
    {
        /** @var User $user */
        $user = auth()->user();

        if (!$user) {
            Log::warning('LoginResponse: No authenticated user found');
            return redirect()->route('filament.admin.auth.login');
        }

        // Log user info for debugging
        Log::info('LoginResponse: User authenticated', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_roles_count' => $user->roles()->count()
        ]);

        // Get user roles
        $roles = $user->roles()->get();

        if ($roles->isEmpty()) {
            Log::warning('LoginResponse: User has no roles assigned', [
                'user_id' => $user->id,
                'user_email' => $user->email
            ]);

            // If user has no roles, assign admin role if they're the first user
            // or if they have is_default_admin flag
            if ($user->is_default_admin || User::count() === 1) {
                try {
                    $adminRole = Role::where('name', Role::ROLE_ADMIN)->first();
                    if ($adminRole) {
                        $user->assignRole($adminRole);
                        Log::info('LoginResponse: Assigned admin role to user', [
                            'user_id' => $user->id
                        ]);
                        return redirect()->route('filament.admin.pages.dashboard');
                    }
                } catch (\Exception $e) {
                    Log::error('LoginResponse: Failed to assign admin role', [
                        'user_id' => $user->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Default to admin dashboard for users without roles
            return redirect()->route('filament.admin.pages.dashboard');
        }

        // Check each role
        foreach ($roles as $role) {
            Log::info('LoginResponse: Checking role', [
                'role_name' => $role->name,
                'role_id' => $role->id
            ]);

            if ($role->name === Role::ROLE_ADMIN) {
                return redirect()->route('filament.admin.pages.dashboard');
            }

            if ($role->name === Role::ROLE_CLIENT) {
                return redirect()->route('filament.client.pages.dashboard');
            }
        }

        // If user has roles but none match expected roles, default to admin
        Log::warning('LoginResponse: User has roles but none match expected roles', [
            'user_id' => $user->id,
            'roles' => $roles->pluck('name')->toArray()
        ]);

        return redirect()->route('filament.admin.pages.dashboard');
    }
}
