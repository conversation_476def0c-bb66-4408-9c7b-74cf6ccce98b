@extends('install.layouts.master')

@section('title', 'Database Setup')

@section('content')
<div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-database text-2xl text-white"></i>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-white">Database Configuration</h1>
                        <p class="text-blue-100 mt-1">Configure your database connection</p>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="px-8 py-6">
                <form action="{{ route('install.database') }}" method="POST" class="space-y-6" x-data="databaseForm()">
                    @csrf

                    <!-- Database Type -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-3">Database Type</label>
                        <div class="grid grid-cols-2 gap-4">
                            <label class="relative">
                                <input type="radio" name="db_connection" value="mysql" x-model="form.db_connection" class="sr-only">
                                <div class="border-2 rounded-lg p-4 cursor-pointer transition-all duration-200"
                                     :class="form.db_connection === 'mysql' ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'">
                                    <div class="flex items-center">
                                        <i class="fas fa-database text-orange-500 text-xl mr-3"></i>
                                        <div>
                                            <div class="font-medium">MySQL</div>
                                            <div class="text-sm text-gray-500">Most common choice</div>
                                        </div>
                                    </div>
                                </div>
                            </label>
                            <label class="relative">
                                <input type="radio" name="db_connection" value="pgsql" x-model="form.db_connection" class="sr-only">
                                <div class="border-2 rounded-lg p-4 cursor-pointer transition-all duration-200"
                                     :class="form.db_connection === 'pgsql' ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'">
                                    <div class="flex items-center">
                                        <i class="fas fa-database text-blue-500 text-xl mr-3"></i>
                                        <div>
                                            <div class="font-medium">PostgreSQL</div>
                                            <div class="text-sm text-gray-500">Advanced features</div>
                                        </div>
                                    </div>
                                </div>
                            </label>
                        </div>
                        @error('db_connection')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Connection Details -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="db_host" class="block text-sm font-medium text-gray-700">Host</label>
                            <input type="text"
                                   id="db_host"
                                   name="db_host"
                                   value="{{ old('db_host', 'localhost') }}"
                                   x-model="form.db_host"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="localhost">
                            @error('db_host')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="db_port" class="block text-sm font-medium text-gray-700">Port</label>
                            <input type="number"
                                   id="db_port"
                                   name="db_port"
                                   value="{{ old('db_port', '3306') }}"
                                   x-model="form.db_port"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="3306">
                            @error('db_port')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div>
                        <label for="db_name" class="block text-sm font-medium text-gray-700">Database Name</label>
                        <input type="text"
                               id="db_name"
                               name="db_name"
                               value="{{ old('db_name') }}"
                               x-model="form.db_name"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                               placeholder="invoices_db">
                        @error('db_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="db_user" class="block text-sm font-medium text-gray-700">Username</label>
                            <input type="text"
                                   id="db_user"
                                   name="db_user"
                                   value="{{ old('db_user') }}"
                                   x-model="form.db_user"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="root">
                            @error('db_user')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="db_pass" class="block text-sm font-medium text-gray-700">Password</label>
                            <input type="password"
                                   id="db_pass"
                                   name="db_pass"
                                   value="{{ old('db_pass') }}"
                                   x-model="form.db_pass"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="Leave empty if no password">
                            @error('db_pass')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Test Connection -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-medium text-gray-900">Test Database Connection</h3>
                            <button type="button"
                                    @click="testConnection()"
                                    :disabled="testing"
                                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
                                <i class="fas fa-plug mr-2" :class="{'loading-spinner': testing}"></i>
                                <span x-text="testing ? 'Testing...' : 'Test Connection'"></span>
                            </button>
                        </div>

                        <div x-show="testResult" class="mt-3">
                            <!-- Success Result -->
                            <div x-show="testResult && testResult.success" class="bg-green-50 border border-green-200 rounded p-3">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <div>
                                        <span class="text-green-800 font-medium">🎉 Connection successful!</span>
                                        <div x-show="testResult.method" class="text-green-700 text-sm mt-1">
                                            Method: <span x-text="testResult.method"></span>
                                        </div>
                                        <div x-show="testResult.warning" class="text-yellow-700 text-sm mt-1" x-text="testResult.warning"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Error Result with BEAST MODE troubleshooting -->
                            <div x-show="testResult && !testResult.success" class="bg-red-50 border border-red-200 rounded p-4">
                                <div class="flex items-start">
                                    <i class="fas fa-times-circle text-red-500 mr-3 mt-0.5"></i>
                                    <div class="flex-1">
                                        <div class="text-red-800 font-medium mb-2">🚨 Connection Failed</div>
                                        <div class="text-red-700 text-sm mb-3" x-text="testResult.error"></div>

                                        <!-- Fix Instructions -->
                                        <div x-show="testResult.fix_instructions" class="mt-4">
                                            <h4 class="font-medium text-red-800 mb-2">🔧 How to Fix:</h4>

                                            <!-- XAMPP Instructions -->
                                            <div x-show="testResult.fix_instructions && testResult.fix_instructions.xampp" class="mb-3">
                                                <h5 class="font-medium text-red-700 mb-1">For XAMPP Users:</h5>
                                                <div class="bg-gray-800 text-green-400 p-2 rounded text-xs font-mono">
                                                    <template x-for="instruction in testResult.fix_instructions.xampp">
                                                        <div x-text="instruction"></div>
                                                    </template>
                                                </div>
                                            </div>

                                            <!-- General Instructions -->
                                            <div x-show="testResult.fix_instructions && testResult.fix_instructions.general" class="mb-3">
                                                <h5 class="font-medium text-red-700 mb-1">General Instructions:</h5>
                                                <div class="bg-gray-800 text-green-400 p-2 rounded text-xs font-mono">
                                                    <template x-for="instruction in testResult.fix_instructions.general">
                                                        <div x-text="instruction"></div>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Technical Details -->
                                        <div x-show="testResult.technical_details" class="mt-3 p-2 bg-gray-100 rounded text-xs text-gray-600">
                                            <strong>Technical Details:</strong> <span x-text="testResult.technical_details"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Error Messages -->
                    @if($errors->any())
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex">
                                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
                                <div>
                                    <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                                    <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Help -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h3 class="font-medium text-blue-800 mb-2">Need Help?</h3>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Make sure your database server is running</li>
                            <li>• Create the database before proceeding if it doesn't exist</li>
                            <li>• For MySQL, default port is 3306. For PostgreSQL, it's 5432</li>
                            <li>• Contact your hosting provider for database credentials</li>
                        </ul>
                    </div>

                    <!-- Shared Hosting Helper -->
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                        <div class="flex items-start">
                            <i class="fas fa-server text-yellow-500 mr-3 mt-0.5"></i>
                            <div class="flex-1">
                                <h3 class="font-medium text-yellow-800 mb-2">🌐 Shared Hosting Users</h3>
                                <p class="text-yellow-700 text-sm mb-3">
                                    If you're using shared hosting (GoDaddy, Bluehost, etc.) and encounter database setup issues,
                                    use our web-based migration tool that doesn't require command-line access.
                                </p>
                                <a href="{{ route('install.web-migration') }}"
                                   class="inline-flex items-center px-4 py-2 border border-yellow-300 text-sm font-medium rounded-md text-yellow-800 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                                    <i class="fas fa-tools mr-2"></i>
                                    🔧 Open Database Setup Tool
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Footer Buttons -->
                    <div class="flex items-center justify-between pt-6 border-t">
                        <a href="{{ route('install.folder-permissions') }}"
                           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back
                        </a>

                        <button type="submit"
                                class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Save & Continue
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function databaseForm() {
    return {
        form: {
            db_connection: 'mysql',
            db_host: 'localhost',
            db_port: '3306',
            db_name: '',
            db_user: '',
            db_pass: ''
        },
        testing: false,
        testResult: null,

        init() {
            // Update port when connection type changes
            this.$watch('form.db_connection', (value) => {
                if (value === 'mysql') {
                    this.form.db_port = '3306';
                } else if (value === 'pgsql') {
                    this.form.db_port = '5432';
                }
            });
        },

        async testConnection() {
            this.testing = true;
            this.testResult = null;

            try {
                const response = await makeRequest('{{ route("install.test-database") }}', this.form);
                this.testResult = response;
            } catch (error) {
                this.testResult = {
                    success: false,
                    error: 'Network error occurred while testing connection'
                };
            }

            this.testing = false;
        }
    }
}
</script>
@endpush
@endsection