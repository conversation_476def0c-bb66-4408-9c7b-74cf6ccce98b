<?php

namespace App\Filament\Resources\ReportsResource\Pages;

use App\Filament\Resources\ReportsResource;
use App\Services\ReportingService;
use App\Models\Currency;
use Filament\Resources\Pages\Page;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Actions\Action;
use Carbon\Carbon;
use Illuminate\Support\Facades\Response;

class CollectionReport extends Page
{
    protected static string $resource = ReportsResource::class;
    
    protected static string $view = 'filament.resources.reports.pages.collection-report';
    
    public ?array $data = [];
    
    public function mount(): void
    {
        $this->form->fill([
            'start_date' => now()->startOfMonth()->format('Y-m-d'),
            'end_date' => now()->endOfMonth()->format('Y-m-d'),
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('start_date')
                    ->label('Start Date')
                    ->required()
                    ->default(now()->startOfMonth()),
                    
                DatePicker::make('end_date')
                    ->label('End Date')
                    ->required()
                    ->default(now()->endOfMonth()),
            ])
            ->statePath('data')
            ->live();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('generate_report')
                ->label('Generate Report')
                ->icon('heroicon-o-arrow-path')
                ->action('generateReport'),
                
            Action::make('export_pdf')
                ->label('Export PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->action('exportPdf'),
                
            Action::make('export_excel')
                ->label('Export Excel')
                ->icon('heroicon-o-table-cells')
                ->action('exportExcel'),
                
            Action::make('back_to_reports')
                ->label('Back to Reports')
                ->icon('heroicon-o-arrow-left')
                ->url(fn () => ReportsResource::getUrl('index'))
                ->color('gray'),
        ];
    }

    public function generateReport(): void
    {
        $this->validate();
        
        $startDate = Carbon::parse($this->data['start_date']);
        $endDate = Carbon::parse($this->data['end_date']);
        
        $reportingService = app(ReportingService::class);
        $this->reportData = $reportingService->getCollectionRateAnalytics($startDate, $endDate);
        
        $this->dispatch('report-generated');
    }

    public function exportPdf()
    {
        $this->generateReport();

        $pdf = app('dompdf.wrapper');
        $pdf->loadView('reports.collection-pdf', [
            'data' => $this->reportData,
            'filters' => $this->data,
        ]);

        return $pdf->download('collection-report-' . now()->format('Y-m-d') . '.pdf');
    }

    public function exportExcel()
    {
        $this->generateReport();

        $csvData = $this->prepareCsvData();

        return Response::streamDownload(function () use ($csvData) {
            echo $csvData;
        }, 'collection-report-' . now()->format('Y-m-d') . '.csv', [
            'Content-Type' => 'text/csv',
        ]);
    }

    protected function prepareCsvData(): string
    {
        $csv = "Collection Rate Report\n";
        $csv .= "Generated: " . now()->format('Y-m-d H:i:s') . "\n";
        $csv .= "Period: {$this->data['start_date']} to {$this->data['end_date']}\n\n";
        
        $csv .= "Summary\n";
        $csv .= "Total Invoiced,{$this->reportData['total_invoiced']}\n";
        $csv .= "Total Collected,{$this->reportData['total_collected']}\n";
        $csv .= "Collection Rate,{$this->reportData['collection_rate']}%\n";
        $csv .= "Average Payment Time,{$this->reportData['average_payment_time']} days\n";
        $csv .= "Paid Invoices Count,{$this->reportData['paid_invoices_count']}\n";
        $csv .= "Total Invoices Count,{$this->reportData['total_invoices_count']}\n";
        
        return $csv;
    }

    protected function getViewData(): array
    {
        if (!isset($this->reportData)) {
            $this->generateReport();
        }
        
        return [
            'reportData' => $this->reportData ?? [],
            'filters' => $this->data,
        ];
    }

    public $reportData = [];
}
