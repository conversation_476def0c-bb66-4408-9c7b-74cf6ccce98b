<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>@yield('title', 'Installation Wizard') - Laravel Invoice System</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .progress-step {
            @apply flex items-center justify-center w-8 h-8 rounded-full border-2 text-sm font-medium transition-all duration-200;
        }
        .progress-step.active {
            @apply bg-blue-600 border-blue-600 text-white;
        }
        .progress-step.completed {
            @apply bg-green-600 border-green-600 text-white;
        }
        .progress-step.inactive {
            @apply bg-gray-200 border-gray-300 text-gray-500;
        }
        .progress-line {
            @apply flex-1 h-0.5 bg-gray-300 transition-all duration-200;
        }
        .progress-line.completed {
            @apply bg-green-600;
        }
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-cogs text-2xl text-blue-600"></i>
                    </div>
                    <div class="ml-3">
                        <h1 class="text-xl font-semibold text-gray-900">Installation Wizard</h1>
                        <p class="text-sm text-gray-500">Laravel Invoice Management System</p>
                    </div>
                </div>
                <div class="text-sm text-gray-500">
                    Version 1.0.0
                </div>
            </div>
        </div>
    </header>

    <!-- Progress Bar -->
    @if(!request()->routeIs('install.index'))
    <div class="bg-white border-b">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                @php
                    $steps = [
                        'install.server-requirements' => ['number' => 1, 'title' => 'Requirements', 'icon' => 'fas fa-server'],
                        'install.folder-permissions' => ['number' => 2, 'title' => 'Permissions', 'icon' => 'fas fa-lock'],
                        'install.database' => ['number' => 3, 'title' => 'Database', 'icon' => 'fas fa-database'],
                        'install.mail' => ['number' => 4, 'title' => 'Mail', 'icon' => 'fas fa-envelope'],
                        'install.admin' => ['number' => 5, 'title' => 'Admin', 'icon' => 'fas fa-user-shield'],
                        'install.branding' => ['number' => 6, 'title' => 'Branding', 'icon' => 'fas fa-palette'],
                        'install.summary' => ['number' => 7, 'title' => 'Summary', 'icon' => 'fas fa-check-circle'],
                    ];
                    $currentRoute = request()->route()->getName();
                    $currentStep = $steps[$currentRoute]['number'] ?? 0;
                @endphp

                @foreach($steps as $route => $step)
                    <div class="flex items-center {{ !$loop->last ? 'flex-1' : '' }}">
                        <div class="flex flex-col items-center">
                            <div class="progress-step {{ $step['number'] < $currentStep ? 'completed' : ($step['number'] == $currentStep ? 'active' : 'inactive') }}">
                                @if($step['number'] < $currentStep)
                                    <i class="fas fa-check"></i>
                                @else
                                    {{ $step['number'] }}
                                @endif
                            </div>
                            <div class="mt-2 text-xs font-medium text-center">
                                <div class="{{ $step['number'] <= $currentStep ? 'text-blue-600' : 'text-gray-500' }}">
                                    <i class="{{ $step['icon'] }} mb-1"></i>
                                </div>
                                <div class="{{ $step['number'] <= $currentStep ? 'text-gray-900' : 'text-gray-500' }}">
                                    {{ $step['title'] }}
                                </div>
                            </div>
                        </div>
                        @if(!$loop->last)
                            <div class="progress-line {{ $step['number'] < $currentStep ? 'completed' : '' }}"></div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Main Content -->
    <main class="flex-1">
        <!-- Flash Messages -->
        @if(session('success'))
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
                <div class="bg-green-50 border border-green-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        @if(session('error'))
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-red-800">{{ session('error') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        @if(session('warning'))
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-yellow-800">{{ session('warning') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-auto">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex justify-between items-center text-sm text-gray-500">
                <div>
                    © {{ date('Y') }} Laravel Invoice Management System
                </div>
                <div class="flex items-center space-x-4">
                    <span>PHP {{ phpversion() }}</span>
                    <span>Laravel {{ app()->version() }}</span>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // 🔥 BEAST MODE: Enhanced CSRF token management
        window.csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        window.csrfRetryCount = 0;
        window.maxCsrfRetries = 3;

        // Function to refresh CSRF token with multiple fallbacks
        window.refreshCsrfToken = function() {
            console.log('🔄 Refreshing CSRF token...');

            return fetch('/install', {
                method: 'GET',
                headers: {
                    'Accept': 'text/html',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Cache-Control': 'no-cache'
                },
                cache: 'no-cache'
            }).then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch new token');
                }
                return response.text();
            })
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newToken = doc.querySelector('meta[name="csrf-token"]');

                if (newToken) {
                    const newTokenValue = newToken.getAttribute('content');
                    window.csrfToken = newTokenValue;

                    // Update meta tag
                    document.querySelector('meta[name="csrf-token"]').setAttribute('content', newTokenValue);

                    // Update all CSRF input fields
                    document.querySelectorAll('input[name="_token"]').forEach(input => {
                        input.value = newTokenValue;
                    });

                    console.log('✅ CSRF token refreshed successfully');
                    return newTokenValue;
                } else {
                    throw new Error('No CSRF token found in response');
                }
            })
            .catch(error => {
                console.error('❌ Failed to refresh CSRF token:', error);

                // Fallback: reload the page if token refresh fails
                if (window.csrfRetryCount < window.maxCsrfRetries) {
                    window.csrfRetryCount++;
                    console.log(`🔄 Retrying CSRF refresh (${window.csrfRetryCount}/${window.maxCsrfRetries})`);
                    return window.refreshCsrfToken();
                } else {
                    console.log('🔄 Max retries reached, reloading page...');
                    window.location.reload();
                }
            });
        };

        // 🔥 BEAST MODE: Enhanced AJAX helper with bulletproof CSRF handling
        window.makeRequest = function(url, data, method = 'POST', retryCount = 0) {
            console.log(`🚀 Making ${method} request to ${url} (attempt ${retryCount + 1})`);

            return fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': window.csrfToken,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Cache-Control': 'no-cache'
                },
                body: method !== 'GET' ? JSON.stringify(data) : undefined,
                cache: 'no-cache'
            }).then(response => {
                console.log(`📡 Response status: ${response.status}`);

                // Handle CSRF token expiration
                if (response.status === 419) {
                    console.log('🚨 CSRF token expired, attempting recovery...');

                    if (retryCount < window.maxCsrfRetries) {
                        return window.refreshCsrfToken().then(() => {
                            console.log('🔄 Retrying request with new token...');
                            return window.makeRequest(url, data, method, retryCount + 1);
                        });
                    } else {
                        console.error('❌ Max CSRF retries exceeded');
                        throw new Error('Session expired. Please refresh the page.');
                    }
                }

                // Parse JSON response
                return response.json().then(jsonData => {
                    if (!response.ok) {
                        throw new Error(jsonData.error || `HTTP ${response.status}`);
                    }
                    return jsonData;
                });
            }).catch(error => {
                console.error('❌ Request failed:', error);

                // Show user-friendly error message
                if (error.message.includes('Session expired')) {
                    alert('Your session has expired. The page will be refreshed.');
                    window.location.reload();
                }

                throw error;
            });
        };

        // Auto-refresh CSRF token every 30 minutes
        setInterval(function() {
            window.refreshCsrfToken().catch(console.error);
        }, 30 * 60 * 1000);

        // Form submission enhancement
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading states to forms
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitButton = form.querySelector('button[type="submit"]');
                    if (submitButton) {
                        submitButton.disabled = true;
                        const originalText = submitButton.innerHTML;
                        submitButton.innerHTML = '<i class="fas fa-spinner loading-spinner mr-2"></i>Processing...';

                        // Re-enable after 30 seconds as fallback
                        setTimeout(() => {
                            submitButton.disabled = false;
                            submitButton.innerHTML = originalText;
                        }, 30000);
                    }
                });
            });

            // Prevent double form submission
            let formSubmitted = false;
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    if (formSubmitted) {
                        e.preventDefault();
                        return false;
                    }
                    formSubmitted = true;

                    // Reset after 5 seconds
                    setTimeout(() => {
                        formSubmitted = false;
                    }, 5000);
                });
            });
        });
    </script>

    @stack('scripts')
</body>
</html>