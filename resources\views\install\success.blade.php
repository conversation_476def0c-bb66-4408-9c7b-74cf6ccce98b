@extends('install.layouts.master')

@section('title', 'Installation Complete')

@section('content')
<div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 to-green-700 px-8 py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-trophy text-3xl text-white"></i>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-3xl font-bold text-white">🎉 BEAST MODE INSTALLATION COMPLETE!</h1>
                        <p class="text-green-100 mt-1">Your Laravel Invoice Management System is ready to rock!</p>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="px-8 py-6">
                <!-- Success Message -->
                <div class="text-center mb-8">
                    <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-green-100 mb-6">
                        <i class="fas fa-check text-4xl text-green-600"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">🚀 Installation Successful!</h2>
                    <p class="text-lg text-gray-600">
                        Your Laravel Invoice Management System has been successfully installed and configured.
                    </p>
                </div>

                <!-- Installation Summary -->
                @if(isset($installationInfo) && $installationInfo)
                    <div class="bg-gray-50 rounded-lg p-6 mb-8">
                        <h3 class="font-semibold text-gray-900 mb-4">📋 Installation Details</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Installed At:</span>
                                <span class="font-medium">{{ \Carbon\Carbon::parse($installationInfo['installed_at'])->format('Y-m-d H:i:s') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Version:</span>
                                <span class="font-medium">{{ $installationInfo['version'] ?? '1.0.0' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">PHP Version:</span>
                                <span class="font-medium">{{ $installationInfo['php_version'] ?? phpversion() }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Laravel Version:</span>
                                <span class="font-medium">{{ $installationInfo['laravel_version'] ?? app()->version() }}</span>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Next Steps -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                    <h3 class="font-semibold text-blue-800 mb-4">🎯 What's Next?</h3>
                    <div class="space-y-3">
                        <div class="flex items-start">
                            <i class="fas fa-sign-in-alt text-blue-600 mr-3 mt-1"></i>
                            <div>
                                <h4 class="font-medium text-blue-800">Access Admin Panel</h4>
                                <p class="text-blue-700 text-sm">Login with your administrator credentials to start managing your invoices</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-cog text-blue-600 mr-3 mt-1"></i>
                            <div>
                                <h4 class="font-medium text-blue-800">Configure Settings</h4>
                                <p class="text-blue-700 text-sm">Customize your application settings, payment methods, and preferences</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-users text-blue-600 mr-3 mt-1"></i>
                            <div>
                                <h4 class="font-medium text-blue-800">Add Team Members</h4>
                                <p class="text-blue-700 text-sm">Invite team members and assign appropriate roles and permissions</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-file-invoice text-blue-600 mr-3 mt-1"></i>
                            <div>
                                <h4 class="font-medium text-blue-800">Create Your First Invoice</h4>
                                <p class="text-blue-700 text-sm">Start creating professional invoices for your clients</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('filament.admin.auth.login') }}"
                       class="inline-flex items-center justify-center px-8 py-4 border border-transparent text-lg font-medium rounded-md text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-lg transform transition hover:scale-105">
                        <i class="fas fa-sign-in-alt mr-3"></i>
                        🚀 Access Admin Panel
                    </a>

                    <a href="{{ url('/') }}"
                       class="inline-flex items-center justify-center px-8 py-4 border border-gray-300 text-lg font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-lg">
                        <i class="fas fa-home mr-3"></i>
                        Visit Homepage
                    </a>
                </div>

                <!-- Celebration -->
                <div class="text-center mt-8">
                    <p class="text-2xl">🎊 🎉 🚀 🔥 ⚡ 🎊 🎉 🚀</p>
                    <p class="text-lg font-medium text-gray-600 mt-2">
                        Welcome to your new Invoice Management System!
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Add some celebration effects
document.addEventListener('DOMContentLoaded', function() {
    // Confetti effect (simple version)
    function createConfetti() {
        const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7'];
        for (let i = 0; i < 50; i++) {
            setTimeout(() => {
                const confetti = document.createElement('div');
                confetti.style.position = 'fixed';
                confetti.style.left = Math.random() * 100 + 'vw';
                confetti.style.top = '-10px';
                confetti.style.width = '10px';
                confetti.style.height = '10px';
                confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.pointerEvents = 'none';
                confetti.style.zIndex = '9999';
                confetti.style.borderRadius = '50%';
                confetti.style.animation = 'fall 3s linear forwards';

                document.body.appendChild(confetti);

                setTimeout(() => {
                    confetti.remove();
                }, 3000);
            }, i * 100);
        }
    }

    // Add CSS animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fall {
            to {
                transform: translateY(100vh) rotate(360deg);
            }
        }
    `;
    document.head.appendChild(style);

    // Trigger confetti
    setTimeout(createConfetti, 500);
});
</script>
@endpush
@endsection