<?php

namespace App\Filament\Resources\ReportsResource\Pages;

use App\Filament\Resources\ReportsResource;
use App\Services\ReportingService;
use Filament\Resources\Pages\Page;
use Filament\Actions\Action;
use Illuminate\Support\Facades\Response;

class ClientAnalysisReport extends Page
{
    protected static string $resource = ReportsResource::class;
    
    protected static string $view = 'filament.resources.reports.pages.client-analysis-report';
    
    public ?array $data = [];
    
    public function mount(): void
    {
        $this->generateReport();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('generate_report')
                ->label('Refresh Report')
                ->icon('heroicon-o-arrow-path')
                ->action('generateReport'),
                
            Action::make('export_pdf')
                ->label('Export PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->action('exportPdf'),
                
            Action::make('export_excel')
                ->label('Export Excel')
                ->icon('heroicon-o-table-cells')
                ->action('exportExcel'),
                
            Action::make('back_to_reports')
                ->label('Back to Reports')
                ->icon('heroicon-o-arrow-left')
                ->url(fn () => ReportsResource::getUrl('index'))
                ->color('gray'),
        ];
    }

    public function generateReport(): void
    {
        $reportingService = app(ReportingService::class);
        $this->reportData = $reportingService->getClientBehaviorAnalysis();
        
        $this->dispatch('report-generated');
    }

    public function exportPdf()
    {
        $this->generateReport();

        $pdf = app('dompdf.wrapper');
        $pdf->loadView('reports.client-analysis-pdf', [
            'data' => $this->reportData,
        ]);

        return $pdf->download('client-analysis-report-' . now()->format('Y-m-d') . '.pdf');
    }

    public function exportExcel()
    {
        $this->generateReport();

        $csvData = $this->prepareCsvData();

        return Response::streamDownload(function () use ($csvData) {
            echo $csvData;
        }, 'client-analysis-report-' . now()->format('Y-m-d') . '.csv', [
            'Content-Type' => 'text/csv',
        ]);
    }

    protected function prepareCsvData(): string
    {
        $csv = "Client Behavior Analysis Report\n";
        $csv .= "Generated: " . now()->format('Y-m-d H:i:s') . "\n\n";
        
        $csv .= "Client Analysis\n";
        $csv .= "Client Name,Total Invoiced,Total Paid,Payment Rate,Average Payment Time,Overdue Amount,Risk Score\n";
        
        foreach ($this->reportData['client_analysis'] as $client) {
            $csv .= "\"{$client['client_name']}\",{$client['total_invoiced']},{$client['total_paid']},{$client['payment_rate']}%,{$client['average_payment_time']},{$client['overdue_amount']},{$client['risk_score']}\n";
        }
        
        return $csv;
    }

    protected function getViewData(): array
    {
        return [
            'reportData' => $this->reportData ?? [],
        ];
    }

    public $reportData = [];
}
