<?php

namespace App\Http\Controllers\Install;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Exception;
use Throwable;

class WebMigrationController extends Controller
{
    /**
     * 🔥 BEAST MODE: Web-based migration tool for shared hosting
     */
    public function index()
    {
        // Check if installation is already complete
        if (file_exists(storage_path('installed'))) {
            return redirect()->route('filament.admin.auth.login')
                ->with('error', 'Installation is already complete.');
        }

        return view('install.web-migration');
    }

    /**
     * 🔥 BEAST MODE: Test endpoint to verify JSON responses
     */
    public function test()
    {
        try {
            header('Content-Type: application/json');

            return response()->json([
                'success' => true,
                'message' => '🔥 BEAST MODE: Web migration tool is working!',
                'timestamp' => now()->toISOString(),
                'php_version' => phpversion(),
                'laravel_version' => app()->version(),
                'database_config' => [
                    'driver' => config('database.default'),
                    'host' => config('database.connections.mysql.host'),
                    'database' => config('database.connections.mysql.database')
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'debug' => [
                    'error_type' => get_class($e),
                    'error_location' => $e->getFile() . ':' . $e->getLine()
                ]
            ]);
        }
    }

    /**
     * 🔥 BEAST MODE: Run migrations via web interface with bulletproof error handling
     */
    public function runMigrations(Request $request)
    {
        // 🔥 BEAST MODE: Ensure we always return JSON, even on fatal errors
        try {
            // Set JSON response headers immediately
            header('Content-Type: application/json');

            $results = [];
            $errors = [];

            $results[] = "🚀 BEAST MODE: Starting web-based migration process";

            // Test database connection first
            try {
                DB::connection()->getPdo();
                $results[] = "✅ Database connection successful";
            } catch (Exception $e) {
                $errors[] = "❌ Database connection failed: " . $e->getMessage();
                return response()->json([
                    'success' => false,
                    'results' => $results,
                    'errors' => $errors,
                    'debug' => 'Database connection test failed'
                ]);
            }

            // Try to get migration files, fallback to manual table creation
            $migrationPath = database_path('migrations');
            $migrationFiles = glob($migrationPath . '/*.php');
            sort($migrationFiles);

            $results[] = "📁 Found " . count($migrationFiles) . " migration files";

            // If no migration files found or if including them fails, use manual table creation
            if (empty($migrationFiles)) {
                $results[] = "⚠️  No migration files found, using manual table creation";
                $this->createTablesManually($results, $errors);
                return response()->json([
                    'success' => empty($errors),
                    'results' => $results,
                    'errors' => $errors,
                    'migrations_run' => 0
                ]);
            }

            // Create migrations table if it doesn't exist
            if (!Schema::hasTable('migrations')) {
                Schema::create('migrations', function ($table) {
                    $table->increments('id');
                    $table->string('migration');
                    $table->integer('batch');
                });
                $results[] = "✅ Migrations table created";
            }

            // Get current batch number
            $batch = DB::table('migrations')->max('batch') + 1;
            $migrationsRun = 0;

            foreach ($migrationFiles as $file) {
                $migrationName = basename($file, '.php');

                // Check if migration already ran
                try {
                    $exists = DB::table('migrations')->where('migration', $migrationName)->exists();
                    if ($exists) {
                        $results[] = "⏭️  Skipping: {$migrationName} (already run)";
                        continue;
                    }
                } catch (Exception $e) {
                    $errors[] = "❌ Error checking migration status for {$migrationName}: " . $e->getMessage();
                    continue;
                }

                try {
                    $results[] = "🔄 Processing: {$migrationName}";

                    // Safely include and run the migration
                    if (!file_exists($file)) {
                        $errors[] = "❌ Migration file not found: {$migrationName}";
                        continue;
                    }

                    // Safely include the migration file
                    ob_start(); // Capture any output
                    $migration = include $file;
                    ob_end_clean(); // Discard output

                    // Verify migration object
                    if (!is_object($migration)) {
                        $errors[] = "❌ Migration file did not return an object: {$migrationName}";
                        continue;
                    }

                    if (!method_exists($migration, 'up')) {
                        $errors[] = "❌ Migration object missing 'up' method: {$migrationName}";
                        continue;
                    }

                    // Run the migration with error handling
                    try {
                        $migration->up();
                    } catch (Exception $migrationError) {
                        $errors[] = "❌ Migration execution failed for {$migrationName}: " . $migrationError->getMessage();
                        continue;
                    }

                    // Record the migration
                    DB::table('migrations')->insert([
                        'migration' => $migrationName,
                        'batch' => $batch
                    ]);

                    $results[] = "✅ Completed: {$migrationName}";
                    $migrationsRun++;

                } catch (Exception $e) {
                    $errors[] = "❌ Failed: {$migrationName} - " . $e->getMessage();
                    $results[] = "🔍 Debug info for {$migrationName}: " . $e->getFile() . ':' . $e->getLine();
                }
            }

            $results[] = "🎉 Migration summary: {$migrationsRun} migrations completed";

            // If no migrations ran successfully, try manual table creation as fallback
            if ($migrationsRun === 0 && !empty($errors)) {
                $results[] = "⚠️  Migration files failed, attempting manual table creation as fallback";
                $this->createTablesManually($results, $errors);
            }

            // Verify critical tables
            $criticalTables = ['users', 'roles', 'permissions', 'cache', 'sessions'];
            $missingTables = [];

            foreach ($criticalTables as $table) {
                if (Schema::hasTable($table)) {
                    $results[] = "✅ Table verified: {$table}";
                } else {
                    $missingTables[] = $table;
                    $errors[] = "❌ Table missing: {$table}";
                }
            }

            $success = empty($missingTables) && empty($errors);

            return response()->json([
                'success' => $success,
                'results' => $results,
                'errors' => $errors,
                'migrations_run' => $migrationsRun
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'results' => $results ?? [],
                'errors' => [
                    '❌ Migration process failed: ' . $e->getMessage(),
                    '🔍 Error location: ' . $e->getFile() . ':' . $e->getLine(),
                    '📋 Stack trace: ' . substr($e->getTraceAsString(), 0, 500)
                ],
                'debug' => [
                    'error_type' => get_class($e),
                    'error_code' => $e->getCode(),
                    'php_version' => phpversion()
                ]
            ]);
        } catch (Throwable $e) {
            // Catch fatal errors and other throwables
            return response()->json([
                'success' => false,
                'results' => [],
                'errors' => [
                    '❌ Fatal error during migration: ' . $e->getMessage(),
                    '🔍 Error location: ' . $e->getFile() . ':' . $e->getLine()
                ],
                'debug' => [
                    'error_type' => get_class($e),
                    'php_version' => phpversion(),
                    'memory_usage' => memory_get_usage(true)
                ]
            ]);
        }
    }

    /**
     * 🔥 BEAST MODE: Run seeders via web interface with bulletproof error handling
     */
    public function runSeeders(Request $request)
    {
        try {
            // Set JSON response headers immediately
            header('Content-Type: application/json');

            $results = [];
            $errors = [];

            $results[] = "🌱 BEAST MODE: Starting web-based seeding process";

            // Ensure essential roles and permissions exist
            $this->ensureEssentialData($results, $errors);

            // Try to run actual seeders if they exist
            $seeders = ['RoleSeeder', 'PermissionSeeder', 'DatabaseSeeder'];

            foreach ($seeders as $seederClass) {
                try {
                    $seederPath = database_path("seeders/{$seederClass}.php");

                    if (file_exists($seederPath)) {
                        require_once $seederPath;

                        if (class_exists($seederClass)) {
                            $seeder = new $seederClass();
                            $seeder->run();
                            $results[] = "✅ Seeder completed: {$seederClass}";
                        } else {
                            $results[] = "⚠️  Seeder class not found: {$seederClass}";
                        }
                    } else {
                        $results[] = "⚠️  Seeder file not found: {$seederClass}";
                    }

                } catch (Exception $e) {
                    $errors[] = "❌ Seeder failed: {$seederClass} - " . $e->getMessage();
                }
            }

            // Verify critical data
            $adminRoleExists = DB::table('roles')->where('name', 'admin')->exists();
            $permissionCount = DB::table('permissions')->count();

            if ($adminRoleExists) {
                $results[] = "✅ Admin role verified";
            } else {
                $errors[] = "❌ Admin role missing";
            }

            if ($permissionCount > 0) {
                $results[] = "✅ Permissions verified ({$permissionCount} found)";
            } else {
                $errors[] = "❌ No permissions found";
            }

            $success = $adminRoleExists && $permissionCount > 0;

            return response()->json([
                'success' => $success,
                'results' => $results,
                'errors' => $errors
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'results' => $results ?? [],
                'errors' => [
                    '❌ Seeding process failed: ' . $e->getMessage(),
                    '🔍 Error location: ' . $e->getFile() . ':' . $e->getLine(),
                    '📋 Stack trace: ' . substr($e->getTraceAsString(), 0, 500)
                ],
                'debug' => [
                    'error_type' => get_class($e),
                    'error_code' => $e->getCode(),
                    'php_version' => phpversion()
                ]
            ]);
        } catch (Throwable $e) {
            // Catch fatal errors and other throwables
            return response()->json([
                'success' => false,
                'results' => [],
                'errors' => [
                    '❌ Fatal error during seeding: ' . $e->getMessage(),
                    '🔍 Error location: ' . $e->getFile() . ':' . $e->getLine()
                ],
                'debug' => [
                    'error_type' => get_class($e),
                    'php_version' => phpversion(),
                    'memory_usage' => memory_get_usage(true)
                ]
            ]);
        }
    }

    /**
     * Ensure essential data exists
     */
    private function ensureEssentialData(&$results, &$errors)
    {
        try {
            // Essential roles
            $essentialRoles = [
                ['name' => 'admin', 'guard_name' => 'web'],
                ['name' => 'user', 'guard_name' => 'web']
            ];

            foreach ($essentialRoles as $role) {
                $exists = DB::table('roles')->where('name', $role['name'])->exists();
                if (!$exists) {
                    DB::table('roles')->insert(array_merge($role, [
                        'created_at' => now(),
                        'updated_at' => now()
                    ]));
                    $results[] = "✅ Created role: {$role['name']}";
                } else {
                    $results[] = "ℹ️  Role exists: {$role['name']}";
                }
            }

            // Essential permissions
            $essentialPermissions = [
                ['name' => 'manage users', 'guard_name' => 'web'],
                ['name' => 'manage roles', 'guard_name' => 'web'],
                ['name' => 'manage invoices', 'guard_name' => 'web'],
                ['name' => 'view dashboard', 'guard_name' => 'web']
            ];

            if (Schema::hasTable('permissions')) {
                foreach ($essentialPermissions as $permission) {
                    $exists = DB::table('permissions')->where('name', $permission['name'])->exists();
                    if (!$exists) {
                        DB::table('permissions')->insert(array_merge($permission, [
                            'created_at' => now(),
                            'updated_at' => now()
                        ]));
                        $results[] = "✅ Created permission: {$permission['name']}";
                    }
                }
            }

        } catch (Exception $e) {
            $errors[] = "❌ Failed to ensure essential data: " . $e->getMessage();
        }
    }

    /**
     * 🔥 BEAST MODE: Get database status with bulletproof error handling
     */
    public function getStatus()
    {
        try {
            // Set JSON response headers immediately
            header('Content-Type: application/json');

            $status = [
                'database_connected' => false,
                'tables_exist' => [],
                'tables_missing' => [],
                'roles_count' => 0,
                'permissions_count' => 0,
                'migrations_count' => 0,
                'php_version' => phpversion(),
                'laravel_version' => app()->version()
            ];

            // Test database connection
            try {
                DB::connection()->getPdo();
                $status['database_connected'] = true;
            } catch (Exception $e) {
                return response()->json(['success' => false, 'error' => 'Database connection failed: ' . $e->getMessage()]);
            }

            // Check critical tables
            $criticalTables = ['users', 'roles', 'permissions', 'cache', 'sessions', 'migrations'];
            foreach ($criticalTables as $table) {
                if (Schema::hasTable($table)) {
                    $status['tables_exist'][] = $table;
                } else {
                    $status['tables_missing'][] = $table;
                }
            }

            // Count data
            if (in_array('roles', $status['tables_exist'])) {
                $status['roles_count'] = DB::table('roles')->count();
            }

            if (in_array('permissions', $status['tables_exist'])) {
                $status['permissions_count'] = DB::table('permissions')->count();
            }

            if (in_array('migrations', $status['tables_exist'])) {
                $status['migrations_count'] = DB::table('migrations')->count();
            }

            return response()->json(['success' => true, 'status' => $status]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'debug' => [
                    'error_type' => get_class($e),
                    'error_location' => $e->getFile() . ':' . $e->getLine(),
                    'php_version' => phpversion()
                ]
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'error' => 'Fatal error: ' . $e->getMessage(),
                'debug' => [
                    'error_type' => get_class($e),
                    'error_location' => $e->getFile() . ':' . $e->getLine(),
                    'php_version' => phpversion()
                ]
            ]);
        }
    }

    /**
     * 🔥 BEAST MODE: Create essential tables manually if migration files fail
     */
    private function createTablesManually(&$results, &$errors)
    {
        try {
            $results[] = "🔧 Creating essential tables manually";

            // Create migrations table
            if (!Schema::hasTable('migrations')) {
                Schema::create('migrations', function (Blueprint $table) {
                    $table->increments('id');
                    $table->string('migration');
                    $table->integer('batch');
                });
                $results[] = "✅ Created migrations table";
            }

            // Create cache table
            if (!Schema::hasTable('cache')) {
                Schema::create('cache', function (Blueprint $table) {
                    $table->string('key')->primary();
                    $table->mediumText('value');
                    $table->integer('expiration');
                });
                $results[] = "✅ Created cache table";
            }

            // Create sessions table
            if (!Schema::hasTable('sessions')) {
                Schema::create('sessions', function (Blueprint $table) {
                    $table->string('id')->primary();
                    $table->foreignId('user_id')->nullable()->index();
                    $table->string('ip_address', 45)->nullable();
                    $table->text('user_agent')->nullable();
                    $table->longText('payload');
                    $table->integer('last_activity')->index();
                });
                $results[] = "✅ Created sessions table";
            }

            // Create users table
            if (!Schema::hasTable('users')) {
                Schema::create('users', function (Blueprint $table) {
                    $table->id();
                    $table->string('name')->nullable();
                    $table->string('first_name');
                    $table->string('last_name');
                    $table->string('email')->unique();
                    $table->string('contact')->nullable();
                    $table->string('region_code')->nullable();
                    $table->boolean('status')->default(1);
                    $table->string('language')->default('en')->nullable();
                    $table->timestamp('email_verified_at')->nullable();
                    $table->string('password');
                    $table->boolean('dark_mode')->default(false);
                    $table->boolean('is_default_admin')->default(false);
                    $table->rememberToken();
                    $table->timestamps();
                });
                $results[] = "✅ Created users table with all required fields";
            } else {
                // Check if name column exists, add it if not
                if (!Schema::hasColumn('users', 'name')) {
                    Schema::table('users', function (Blueprint $table) {
                        $table->string('name')->nullable()->after('id');
                    });
                    $results[] = "✅ Added name column to existing users table";
                }
            }

            // Create password reset tokens table
            if (!Schema::hasTable('password_reset_tokens')) {
                Schema::create('password_reset_tokens', function (Blueprint $table) {
                    $table->string('email')->primary();
                    $table->string('token');
                    $table->timestamp('created_at')->nullable();
                });
                $results[] = "✅ Created password_reset_tokens table";
            }

            // Create failed jobs table
            if (!Schema::hasTable('failed_jobs')) {
                Schema::create('failed_jobs', function (Blueprint $table) {
                    $table->id();
                    $table->string('uuid')->unique();
                    $table->text('connection');
                    $table->text('queue');
                    $table->longText('payload');
                    $table->longText('exception');
                    $table->timestamp('failed_at')->useCurrent();
                });
                $results[] = "✅ Created failed_jobs table";
            }

            // Create roles table (for Spatie Laravel Permission)
            if (!Schema::hasTable('roles')) {
                Schema::create('roles', function (Blueprint $table) {
                    $table->bigIncrements('id');
                    $table->string('name');
                    $table->string('guard_name');
                    $table->timestamps();
                    $table->unique(['name', 'guard_name']);
                });
                $results[] = "✅ Created roles table";
            }

            // Create permissions table
            if (!Schema::hasTable('permissions')) {
                Schema::create('permissions', function (Blueprint $table) {
                    $table->bigIncrements('id');
                    $table->string('name');
                    $table->string('guard_name');
                    $table->timestamps();
                    $table->unique(['name', 'guard_name']);
                });
                $results[] = "✅ Created permissions table";
            }

            // Create model_has_permissions table
            if (!Schema::hasTable('model_has_permissions')) {
                Schema::create('model_has_permissions', function (Blueprint $table) {
                    $table->unsignedBigInteger('permission_id');
                    $table->string('model_type');
                    $table->unsignedBigInteger('model_id');
                    $table->index(['model_id', 'model_type']);
                    $table->foreign('permission_id')->references('id')->on('permissions')->onDelete('cascade');
                    $table->primary(['permission_id', 'model_id', 'model_type']);
                });
                $results[] = "✅ Created model_has_permissions table";
            }

            // Create model_has_roles table
            if (!Schema::hasTable('model_has_roles')) {
                Schema::create('model_has_roles', function (Blueprint $table) {
                    $table->unsignedBigInteger('role_id');
                    $table->string('model_type');
                    $table->unsignedBigInteger('model_id');
                    $table->index(['model_id', 'model_type']);
                    $table->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');
                    $table->primary(['role_id', 'model_id', 'model_type']);
                });
                $results[] = "✅ Created model_has_roles table";
            }

            // Create role_has_permissions table
            if (!Schema::hasTable('role_has_permissions')) {
                Schema::create('role_has_permissions', function (Blueprint $table) {
                    $table->unsignedBigInteger('permission_id');
                    $table->unsignedBigInteger('role_id');
                    $table->foreign('permission_id')->references('id')->on('permissions')->onDelete('cascade');
                    $table->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');
                    $table->primary(['permission_id', 'role_id']);
                });
                $results[] = "✅ Created role_has_permissions table";
            }

            $results[] = "🎉 Manual table creation completed successfully";

        } catch (Exception $e) {
            $errors[] = "❌ Manual table creation failed: " . $e->getMessage();
        }
    }
}
