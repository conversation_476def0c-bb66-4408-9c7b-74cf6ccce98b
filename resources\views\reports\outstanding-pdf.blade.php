<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Outstanding Invoices Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #dc2626;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .summary-section {
            margin-bottom: 25px;
        }
        .summary-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .summary-item {
            display: table-cell;
            width: 25%;
            padding: 10px;
            border: 1px solid #ddd;
            text-align: center;
            vertical-align: top;
        }
        .summary-item h3 {
            margin: 0 0 5px 0;
            font-size: 14px;
            color: #666;
        }
        .summary-item .value {
            font-size: 18px;
            font-weight: bold;
        }
        .value.outstanding {
            color: #f59e0b;
        }
        .value.overdue {
            color: #dc2626;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0 10px 0;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .status-outstanding {
            color: #f59e0b;
            font-weight: bold;
        }
        .status-overdue {
            color: #dc2626;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .alert {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            color: #991b1b;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Outstanding Invoices Report</h1>
        <p>Generated on: {{ now()->format('F j, Y \a\t g:i A') }}</p>
        @if(isset($filters['currency_id']) && $filters['currency_id'])
        <p>Currency Filter Applied</p>
        @endif
    </div>

    <!-- Alert for Overdue Invoices -->
    @if(($data['overdue_amount'] ?? 0) > 0)
    <div class="alert">
        <strong>Alert:</strong> You have {{ getCurrencyAmount($data['overdue_amount'], true) }} in overdue invoices that require immediate attention.
    </div>
    @endif

    <!-- Summary Section -->
    <div class="summary-section">
        <div class="section-title">Outstanding Summary</div>
        <div class="summary-grid">
            <div class="summary-item">
                <h3>Outstanding Amount</h3>
                <div class="value outstanding">{{ getCurrencyAmount($data['outstanding_amount'] ?? 0, true) }}</div>
            </div>
            <div class="summary-item">
                <h3>Overdue Amount</h3>
                <div class="value overdue">{{ getCurrencyAmount($data['overdue_amount'] ?? 0, true) }}</div>
            </div>
            <div class="summary-item">
                <h3>Outstanding Count</h3>
                <div class="value outstanding">{{ number_format($data['outstanding_count'] ?? 0) }}</div>
            </div>
            <div class="summary-item">
                <h3>Overdue Count</h3>
                <div class="value overdue">{{ number_format($data['overdue_count'] ?? 0) }}</div>
            </div>
        </div>
    </div>

    <!-- Outstanding Invoices by Client -->
    @if(isset($data['outstanding_by_client']) && !empty($data['outstanding_by_client']))
    <div class="section-title">Outstanding Invoices by Client</div>
    <table>
        <thead>
            <tr>
                <th>Client Name</th>
                <th class="text-center">Outstanding Count</th>
                <th class="text-center">Overdue Count</th>
                <th class="text-right">Outstanding Amount</th>
                <th class="text-right">Overdue Amount</th>
                <th class="text-center">Risk Level</th>
            </tr>
        </thead>
        <tbody>
            @foreach($data['outstanding_by_client'] as $client)
            <tr>
                <td>{{ $client['client_name'] }}</td>
                <td class="text-center">{{ $client['outstanding_count'] }}</td>
                <td class="text-center">{{ $client['overdue_count'] }}</td>
                <td class="text-right">{{ getCurrencyAmount($client['outstanding_amount'], true) }}</td>
                <td class="text-right">{{ getCurrencyAmount($client['overdue_amount'], true) }}</td>
                <td class="text-center">
                    @if($client['overdue_amount'] > 0)
                        <span class="status-overdue">High</span>
                    @elseif($client['outstanding_amount'] > 1000)
                        <span class="status-outstanding">Medium</span>
                    @else
                        Low
                    @endif
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <!-- Aging Analysis -->
    @if(isset($data['aging_analysis']) && !empty($data['aging_analysis']))
    <div class="section-title">Aging Analysis</div>
    <table>
        <thead>
            <tr>
                <th>Age Range</th>
                <th class="text-center">Invoice Count</th>
                <th class="text-right">Total Amount</th>
                <th class="text-right">Percentage</th>
            </tr>
        </thead>
        <tbody>
            @foreach($data['aging_analysis'] as $age => $analysis)
            <tr>
                <td>{{ $age }}</td>
                <td class="text-center">{{ $analysis['count'] }}</td>
                <td class="text-right">{{ getCurrencyAmount($analysis['amount'], true) }}</td>
                <td class="text-right">{{ number_format($analysis['percentage'], 1) }}%</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <!-- Recent Outstanding Invoices -->
    @if(isset($data['recent_outstanding']) && !empty($data['recent_outstanding']))
    <div class="section-title">Recent Outstanding Invoices</div>
    <table>
        <thead>
            <tr>
                <th>Invoice #</th>
                <th>Client</th>
                <th>Issue Date</th>
                <th>Due Date</th>
                <th class="text-right">Amount</th>
                <th class="text-center">Status</th>
                <th class="text-center">Days Overdue</th>
            </tr>
        </thead>
        <tbody>
            @foreach(array_slice($data['recent_outstanding'], 0, 20) as $invoice)
            <tr>
                <td>{{ $invoice['invoice_number'] }}</td>
                <td>{{ $invoice['client_name'] }}</td>
                <td>{{ \Carbon\Carbon::parse($invoice['invoice_date'])->format('M j, Y') }}</td>
                <td>{{ \Carbon\Carbon::parse($invoice['due_date'])->format('M j, Y') }}</td>
                <td class="text-right">{{ getCurrencyAmount($invoice['outstanding_amount'], true) }}</td>
                <td class="text-center">
                    @if($invoice['status'] === 'overdue')
                        <span class="status-overdue">Overdue</span>
                    @else
                        <span class="status-outstanding">Outstanding</span>
                    @endif
                </td>
                <td class="text-center">
                    @if($invoice['days_overdue'] > 0)
                        <span class="status-overdue">{{ $invoice['days_overdue'] }}</span>
                    @else
                        -
                    @endif
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <div class="footer">
        <p>This report was generated automatically by the Invoice Management System</p>
        <p>{{ config('app.name') }} - Outstanding Invoices Analytics</p>
    </div>
</body>
</html>
