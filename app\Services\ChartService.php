<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Collection;

class ChartService
{
    /**
     * Generate revenue chart data for Chart.js
     */
    public function generateRevenueChart(array $revenueData): array
    {
        $dailyRevenue = $revenueData['daily_revenue'] ?? [];
        
        // Prepare data for Chart.js
        $labels = [];
        $data = [];
        
        foreach ($dailyRevenue as $date => $amount) {
            $labels[] = Carbon::parse($date)->format('M d');
            $data[] = (float) $amount;
        }
        
        return [
            'type' => 'line',
            'data' => [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => 'Daily Revenue',
                        'data' => $data,
                        'borderColor' => 'rgb(59, 130, 246)',
                        'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                        'tension' => 0.4,
                        'fill' => true,
                        'pointBackgroundColor' => 'rgb(59, 130, 246)',
                        'pointBorderColor' => '#ffffff',
                        'pointBorderWidth' => 2,
                        'pointRadius' => 4
                    ]
                ]
            ],
            'options' => [
                'responsive' => true,
                'maintainAspectRatio' => false,
                'plugins' => [
                    'legend' => [
                        'display' => true,
                        'position' => 'top'
                    ],
                    'tooltip' => [
                        'mode' => 'index',
                        'intersect' => false,
                        'backgroundColor' => 'rgba(0, 0, 0, 0.8)',
                        'titleColor' => '#ffffff',
                        'bodyColor' => '#ffffff',
                        'borderColor' => 'rgb(59, 130, 246)',
                        'borderWidth' => 1
                    ]
                ],
                'scales' => [
                    'x' => [
                        'grid' => [
                            'display' => false
                        ],
                        'ticks' => [
                            'color' => '#6b7280'
                        ]
                    ],
                    'y' => [
                        'beginAtZero' => true,
                        'grid' => [
                            'color' => 'rgba(107, 114, 128, 0.1)'
                        ],
                        'ticks' => [
                            'color' => '#6b7280',
                            'callback' => 'function(value) { return "$" + value.toLocaleString(); }'
                        ]
                    ]
                ],
                'interaction' => [
                    'intersect' => false,
                    'mode' => 'index'
                ]
            ]
        ];
    }

    /**
     * Generate outstanding invoices pie chart
     */
    public function generateOutstandingChart(array $outstandingData): array
    {
        $statusBreakdown = $outstandingData['status_breakdown'] ?? [];
        
        $labels = [];
        $data = [];
        $colors = [
            'Overdue' => '#ef4444',
            'Unpaid' => '#f59e0b', 
            'Partially Paid' => '#10b981',
            'Draft' => '#6b7280'
        ];
        
        $backgroundColors = [];
        
        foreach ($statusBreakdown as $status => $amount) {
            $labels[] = ucfirst($status);
            $data[] = (float) $amount;
            $backgroundColors[] = $colors[$status] ?? '#6b7280';
        }
        
        return [
            'type' => 'doughnut',
            'data' => [
                'labels' => $labels,
                'datasets' => [
                    [
                        'data' => $data,
                        'backgroundColor' => $backgroundColors,
                        'borderWidth' => 2,
                        'borderColor' => '#ffffff',
                        'hoverBorderWidth' => 3,
                        'hoverBorderColor' => '#ffffff'
                    ]
                ]
            ],
            'options' => [
                'responsive' => true,
                'maintainAspectRatio' => false,
                'plugins' => [
                    'legend' => [
                        'position' => 'bottom',
                        'labels' => [
                            'padding' => 20,
                            'usePointStyle' => true,
                            'color' => '#374151'
                        ]
                    ],
                    'tooltip' => [
                        'backgroundColor' => 'rgba(0, 0, 0, 0.8)',
                        'titleColor' => '#ffffff',
                        'bodyColor' => '#ffffff',
                        'callbacks' => [
                            'label' => 'function(context) { 
                                return context.label + ": $" + context.parsed.toLocaleString(); 
                            }'
                        ]
                    ]
                ],
                'cutout' => '60%'
            ]
        ];
    }

    /**
     * Generate collection rate trend chart
     */
    public function generateCollectionChart(array $collectionData): array
    {
        // Sample data - in real implementation, this would come from the service
        $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
        $collectionRates = [85, 92, 78, 88, 95, 90];
        
        return [
            'type' => 'bar',
            'data' => [
                'labels' => $months,
                'datasets' => [
                    [
                        'label' => 'Collection Rate %',
                        'data' => $collectionRates,
                        'backgroundColor' => 'rgba(34, 197, 94, 0.8)',
                        'borderColor' => 'rgb(34, 197, 94)',
                        'borderWidth' => 1,
                        'borderRadius' => 4,
                        'borderSkipped' => false
                    ]
                ]
            ],
            'options' => [
                'responsive' => true,
                'maintainAspectRatio' => false,
                'plugins' => [
                    'legend' => [
                        'display' => false
                    ],
                    'tooltip' => [
                        'backgroundColor' => 'rgba(0, 0, 0, 0.8)',
                        'titleColor' => '#ffffff',
                        'bodyColor' => '#ffffff',
                        'callbacks' => [
                            'label' => 'function(context) { 
                                return "Collection Rate: " + context.parsed.y + "%"; 
                            }'
                        ]
                    ]
                ],
                'scales' => [
                    'x' => [
                        'grid' => [
                            'display' => false
                        ],
                        'ticks' => [
                            'color' => '#6b7280'
                        ]
                    ],
                    'y' => [
                        'beginAtZero' => true,
                        'max' => 100,
                        'grid' => [
                            'color' => 'rgba(107, 114, 128, 0.1)'
                        ],
                        'ticks' => [
                            'color' => '#6b7280',
                            'callback' => 'function(value) { return value + "%"; }'
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * Generate client revenue comparison chart
     */
    public function generateClientRevenueChart(array $clientData): array
    {
        $clients = array_slice($clientData['revenue_by_client'] ?? [], 0, 10); // Top 10 clients
        
        $labels = [];
        $data = [];
        
        foreach ($clients as $client) {
            $labels[] = $client['client_name'] ?? 'Unknown';
            $data[] = (float) ($client['total_revenue'] ?? 0);
        }
        
        return [
            'type' => 'horizontalBar',
            'data' => [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => 'Revenue',
                        'data' => $data,
                        'backgroundColor' => 'rgba(147, 51, 234, 0.8)',
                        'borderColor' => 'rgb(147, 51, 234)',
                        'borderWidth' => 1
                    ]
                ]
            ],
            'options' => [
                'responsive' => true,
                'maintainAspectRatio' => false,
                'indexAxis' => 'y',
                'plugins' => [
                    'legend' => [
                        'display' => false
                    ],
                    'tooltip' => [
                        'backgroundColor' => 'rgba(0, 0, 0, 0.8)',
                        'titleColor' => '#ffffff',
                        'bodyColor' => '#ffffff'
                    ]
                ],
                'scales' => [
                    'x' => [
                        'beginAtZero' => true,
                        'grid' => [
                            'color' => 'rgba(107, 114, 128, 0.1)'
                        ],
                        'ticks' => [
                            'color' => '#6b7280',
                            'callback' => 'function(value) { return "$" + value.toLocaleString(); }'
                        ]
                    ],
                    'y' => [
                        'grid' => [
                            'display' => false
                        ],
                        'ticks' => [
                            'color' => '#6b7280'
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * Generate monthly revenue comparison chart
     */
    public function generateMonthlyComparisonChart(array $currentYear, array $previousYear): array
    {
        $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        
        return [
            'type' => 'line',
            'data' => [
                'labels' => $months,
                'datasets' => [
                    [
                        'label' => date('Y') . ' Revenue',
                        'data' => $currentYear,
                        'borderColor' => 'rgb(59, 130, 246)',
                        'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                        'tension' => 0.4
                    ],
                    [
                        'label' => (date('Y') - 1) . ' Revenue',
                        'data' => $previousYear,
                        'borderColor' => 'rgb(156, 163, 175)',
                        'backgroundColor' => 'rgba(156, 163, 175, 0.1)',
                        'tension' => 0.4,
                        'borderDash' => [5, 5]
                    ]
                ]
            ],
            'options' => [
                'responsive' => true,
                'maintainAspectRatio' => false,
                'plugins' => [
                    'legend' => [
                        'display' => true,
                        'position' => 'top'
                    ]
                ],
                'scales' => [
                    'x' => [
                        'grid' => [
                            'display' => false
                        ]
                    ],
                    'y' => [
                        'beginAtZero' => true,
                        'grid' => [
                            'color' => 'rgba(107, 114, 128, 0.1)'
                        ]
                    ]
                ]
            ]
        ];
    }
}
