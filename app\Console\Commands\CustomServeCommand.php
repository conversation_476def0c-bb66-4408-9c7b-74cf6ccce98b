<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Process\Process;

class CustomServeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'serve:custom 
                            {--host=127.0.0.1 : The host address to serve the application on}
                            {--port=8000 : The port to serve the application on}
                            {--tries=0 : The max number of ports to attempt to serve on}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Start the Laravel development server (Windows/XAMPP compatible)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $host = $this->option('host');
        $port = (int) $this->option('port');
        $tries = (int) $this->option('tries');

        // Validate host and port
        if (!$this->isValidHost($host)) {
            $this->error("Invalid host: {$host}");
            return 1;
        }

        if ($port < 1 || $port > 65535) {
            $this->error("Invalid port: {$port}. Port must be between 1 and 65535.");
            return 1;
        }

        // Find available port
        $originalPort = $port;
        $attempts = 0;
        
        while ($attempts <= $tries) {
            if ($this->isPortAvailable($host, $port)) {
                break;
            }
            
            if ($attempts < $tries) {
                $previousPort = $port - 1;
                $port++;
                $attempts++;
                $this->warn("Port {$previousPort} is in use, trying port {$port}...");
            } else {
                $this->error("Unable to find an available port after {$tries} attempts.");
                return 1;
            }
        }

        $this->info("🚀 Starting Laravel development server...");
        $this->info("📍 Server: http://{$host}:{$port}");
        $this->info("📁 Document Root: " . public_path());
        $this->info("🛑 Press Ctrl+C to stop the server");
        $this->line('');

        // Prepare the command
        $command = $this->buildServeCommand($host, $port);

        // Start the server
        $process = new Process($command, base_path(), null, null, null);
        $process->setTimeout(null);

        try {
            $process->run(function ($type, $buffer) {
                $this->output->write($buffer);
            });
        } catch (\Exception $e) {
            $this->error("Server error: " . $e->getMessage());
            return 1;
        }

        return $process->getExitCode();
    }

    /**
     * Build the serve command array
     */
    private function buildServeCommand(string $host, int $port): array
    {
        $serverScript = $this->getServerScript();
        
        return [
            PHP_BINARY,
            '-S',
            "{$host}:{$port}",
            '-t',
            public_path(),
            $serverScript
        ];
    }

    /**
     * Get the server script path
     */
    private function getServerScript(): string
    {
        // Check if server.php exists in base path
        $serverScript = base_path('server.php');
        
        if (!file_exists($serverScript)) {
            // Create a basic server.php if it doesn't exist
            $this->createServerScript($serverScript);
        }
        
        return $serverScript;
    }

    /**
     * Create a basic server.php script
     */
    private function createServerScript(string $path): void
    {
        $content = <<<'PHP'
<?php

/**
 * Laravel - A PHP Framework For Web Artisans
 *
 * @package  Laravel
 * <AUTHOR> Otwell <<EMAIL>>
 */

$uri = urldecode(
    parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH) ?? ''
);

// This file allows us to emulate Apache's "mod_rewrite" functionality from the
// built-in PHP web server. This provides a convenient way to test a Laravel
// application without having installed a "real" web server software here.
if ($uri !== '/' && file_exists(__DIR__.'/public'.$uri)) {
    return false;
}

require_once __DIR__.'/public/index.php';
PHP;

        file_put_contents($path, $content);
        $this->info("Created server.php script at: {$path}");
    }

    /**
     * Check if the host is valid
     */
    private function isValidHost(string $host): bool
    {
        // Allow localhost, IP addresses, and domain names
        return filter_var($host, FILTER_VALIDATE_IP) !== false || 
               $host === 'localhost' || 
               preg_match('/^[a-zA-Z0-9.-]+$/', $host);
    }

    /**
     * Check if a port is available
     */
    private function isPortAvailable(string $host, int $port): bool
    {
        $connection = @fsockopen($host, $port, $errno, $errstr, 1);
        
        if (is_resource($connection)) {
            fclose($connection);
            return false; // Port is in use
        }
        
        return true; // Port is available
    }
}
