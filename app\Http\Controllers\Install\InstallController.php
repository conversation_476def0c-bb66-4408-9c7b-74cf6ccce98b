<?php

namespace App\Http\Controllers\Install;

use App\Http\Controllers\Controller;
use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Exception;
use PDO;
use PDOException;

class InstallController extends Controller
{
    private $logFile;

    public function __construct()
    {
        $this->logFile = storage_path('logs/install.log');

        // Ensure log directory exists
        if (!File::exists(dirname($this->logFile))) {
            File::makeDirectory(dirname($this->logFile), 0755, true);
        }
    }

    /**
     * Log installation steps and errors
     */
    private function logInstallation($message, $level = 'info', $context = [])
    {
        $timestamp = now()->format('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] [{$level}] {$message}";

        if (!empty($context)) {
            $logMessage .= ' Context: ' . json_encode($context);
        }

        File::append($this->logFile, $logMessage . PHP_EOL);

        // Also log to Laravel's default log
        Log::channel('single')->{$level}($message, $context);
    }

    public function index()
    {
        $this->logInstallation('Installation wizard started');

        // Check if already installed
        if ($this->isInstalled()) {
            $this->logInstallation('Installation already completed, redirecting to login');
            return redirect()->route('filament.admin.auth.login')
                ->with('warning', 'Application is already installed.');
        }

        return view('install.index');
    }

    /**
     * Check if application is already installed
     */
    private function isInstalled()
    {
        return File::exists(storage_path('installed'));
    }

    public function serverRequirements()
    {
        $this->logInstallation('Checking server requirements');

        $phpVersion = [
            'full' => phpversion(),
            'current' => substr(phpversion(), 0, 3),
            'supported' => version_compare(phpversion(), '8.1', '>='),
        ];

        $phpExtensions = [
            'openssl' => [
                'loaded' => extension_loaded('openssl'),
                'required' => true,
                'description' => 'Required for secure connections and encryption'
            ],
            'pdo' => [
                'loaded' => extension_loaded('pdo'),
                'required' => true,
                'description' => 'Required for database connections'
            ],
            'pdo_mysql' => [
                'loaded' => extension_loaded('pdo_mysql'),
                'required' => true,
                'description' => 'Required for MySQL database connections'
            ],
            'pdo_pgsql' => [
                'loaded' => extension_loaded('pdo_pgsql'),
                'required' => false,
                'description' => 'Optional for PostgreSQL database connections'
            ],
            'mbstring' => [
                'loaded' => extension_loaded('mbstring'),
                'required' => true,
                'description' => 'Required for string manipulation'
            ],
            'tokenizer' => [
                'loaded' => extension_loaded('tokenizer'),
                'required' => true,
                'description' => 'Required for PHP tokenization'
            ],
            'xml' => [
                'loaded' => extension_loaded('xml'),
                'required' => true,
                'description' => 'Required for XML processing'
            ],
            'ctype' => [
                'loaded' => extension_loaded('ctype'),
                'required' => true,
                'description' => 'Required for character type checking'
            ],
            'fileinfo' => [
                'loaded' => extension_loaded('fileinfo'),
                'required' => true,
                'description' => 'Required for file type detection'
            ],
            'bcmath' => [
                'loaded' => extension_loaded('bcmath'),
                'required' => true,
                'description' => 'Required for arbitrary precision mathematics'
            ],
            'curl' => [
                'loaded' => extension_loaded('curl'),
                'required' => true,
                'description' => 'Required for HTTP requests'
            ],
            'gd' => [
                'loaded' => extension_loaded('gd'),
                'required' => true,
                'description' => 'Required for image processing'
            ],
            'zip' => [
                'loaded' => extension_loaded('zip'),
                'required' => false,
                'description' => 'Optional for ZIP file handling'
            ],
            'json' => [
                'loaded' => extension_loaded('json'),
                'required' => true,
                'description' => 'Required for JSON processing'
            ],
        ];

        // Additional server checks
        $serverChecks = [
            'memory_limit' => [
                'current' => ini_get('memory_limit'),
                'recommended' => '256M',
                'status' => $this->checkMemoryLimit(ini_get('memory_limit'), '256M')
            ],
            'max_execution_time' => [
                'current' => ini_get('max_execution_time'),
                'recommended' => '300',
                'status' => (int)ini_get('max_execution_time') >= 300 || ini_get('max_execution_time') == 0
            ],
            'upload_max_filesize' => [
                'current' => ini_get('upload_max_filesize'),
                'recommended' => '10M',
                'status' => $this->checkFileSize(ini_get('upload_max_filesize'), '10M')
            ],
            'composer' => [
                'status' => $this->checkComposer(),
                'message' => $this->checkComposer() ? 'Available' : 'Not available (using pre-installed vendor)'
            ]
        ];

        // Check if all required extensions are loaded
        $requiredExtensionsMet = true;
        foreach ($phpExtensions as $extension => $details) {
            if ($details['required'] && !$details['loaded']) {
                $requiredExtensionsMet = false;
                break;
            }
        }

        $allRequirementsMet = $phpVersion['supported'] &&
                             $requiredExtensionsMet &&
                             $serverChecks['memory_limit']['status'] &&
                             $serverChecks['upload_max_filesize']['status'];

        $this->logInstallation('Server requirements check completed', 'info', [
            'php_version' => $phpVersion,
            'extensions' => $phpExtensions,
            'server_checks' => $serverChecks,
            'all_met' => $allRequirementsMet
        ]);

        return view('install.server-requirements', compact(
            'phpVersion',
            'phpExtensions',
            'serverChecks',
            'allRequirementsMet'
        ));
    }

    /**
     * Check memory limit
     */
    private function checkMemoryLimit($current, $required)
    {
        $currentBytes = $this->convertToBytes($current);
        $requiredBytes = $this->convertToBytes($required);

        return $currentBytes >= $requiredBytes || $current === '-1';
    }

    /**
     * Check file size limit
     */
    private function checkFileSize($current, $required)
    {
        $currentBytes = $this->convertToBytes($current);
        $requiredBytes = $this->convertToBytes($required);

        return $currentBytes >= $requiredBytes;
    }

    /**
     * Convert size string to bytes
     */
    private function convertToBytes($size)
    {
        $size = trim($size);
        $last = strtolower($size[strlen($size) - 1]);
        $size = (int) $size;

        switch ($last) {
            case 'g':
                $size *= 1024;
            case 'm':
                $size *= 1024;
            case 'k':
                $size *= 1024;
        }

        return $size;
    }

    /**
     * Check if Composer is available
     */
    private function checkComposer()
    {
        $composerPath = base_path('composer.phar');
        if (file_exists($composerPath)) {
            return true;
        }

        // Check if composer is in PATH
        $output = shell_exec('composer --version 2>&1');
        return $output !== null && strpos($output, 'Composer') !== false;
    }

    public function folderPermissions()
    {
        $this->logInstallation('Checking folder permissions');

        $folderPermissions = [
            'storage' => [
                'path' => storage_path(),
                'writable' => is_writable(storage_path()),
                'permission' => substr(sprintf('%o', fileperms(storage_path())), -4)
            ],
            'bootstrap/cache' => [
                'path' => base_path('bootstrap/cache'),
                'writable' => is_writable(base_path('bootstrap/cache')),
                'permission' => substr(sprintf('%o', fileperms(base_path('bootstrap/cache'))), -4)
            ],
            'storage/app' => [
                'path' => storage_path('app'),
                'writable' => is_writable(storage_path('app')),
                'permission' => substr(sprintf('%o', fileperms(storage_path('app'))), -4)
            ],
            'storage/framework' => [
                'path' => storage_path('framework'),
                'writable' => is_writable(storage_path('framework')),
                'permission' => substr(sprintf('%o', fileperms(storage_path('framework'))), -4)
            ],
            'storage/logs' => [
                'path' => storage_path('logs'),
                'writable' => is_writable(storage_path('logs')),
                'permission' => substr(sprintf('%o', fileperms(storage_path('logs'))), -4)
            ],
            'public' => [
                'path' => public_path(),
                'writable' => is_writable(public_path()),
                'permission' => substr(sprintf('%o', fileperms(public_path())), -4)
            ],
        ];

        // Check .env file
        $envFile = base_path('.env');
        $envPermissions = [
            'exists' => file_exists($envFile),
            'writable' => file_exists($envFile) ? is_writable($envFile) : false,
            'permission' => file_exists($envFile) ? substr(sprintf('%o', fileperms($envFile)), -4) : 'N/A'
        ];

        $allPermissionsOk = collect($folderPermissions)->every(function ($folder) {
            return $folder['writable'];
        }) && $envPermissions['writable'];

        $this->logInstallation('Folder permissions check completed', 'info', [
            'folders' => $folderPermissions,
            'env_file' => $envPermissions,
            'all_ok' => $allPermissionsOk
        ]);

        return view('install.folder-permissions', compact(
            'folderPermissions',
            'envPermissions',
            'allPermissionsOk'
        ));
    }

    public function database()
    {
        $this->logInstallation('Displaying database configuration form');
        return view('install.database');
    }

    public function saveDatabase(Request $request)
    {
        $this->logInstallation('Processing database configuration', 'info', [
            'host' => $request->db_host,
            'database' => $request->db_name,
            'username' => $request->db_user
        ]);

        $request->validate([
            'db_connection' => 'required|in:mysql,pgsql',
            'db_host' => 'required',
            'db_port' => 'required|numeric',
            'db_name' => 'required',
            'db_user' => 'required',
            'db_pass' => 'nullable',
        ]);

        // Test database connection
        $connectionResult = $this->testDatabaseConnection(
            $request->db_connection,
            $request->db_host,
            $request->db_port,
            $request->db_name,
            $request->db_user,
            $request->db_pass
        );

        if (!$connectionResult['success']) {
            $this->logInstallation('Database connection failed', 'error', [
                'error' => $connectionResult['error']
            ]);

            return redirect()->back()
                ->withErrors(['db_connection' => $connectionResult['error']])
                ->withInput();
        }

        // Update .env file
        try {
            $this->updateEnvFile([
                'DB_CONNECTION' => $request->db_connection,
                'DB_HOST' => $request->db_host,
                'DB_PORT' => $request->db_port,
                'DB_DATABASE' => $request->db_name,
                'DB_USERNAME' => $request->db_user,
                'DB_PASSWORD' => $request->db_pass,
            ]);

            $this->logInstallation('Database configuration saved successfully');

            return redirect()->route('install.mail')
                ->with('success', 'Database configuration saved successfully.');

        } catch (Exception $e) {
            $this->logInstallation('Failed to update .env file', 'error', [
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->withErrors(['env_update' => 'Failed to update configuration file: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Test database connection with BEAST MODE error handling
     */
    private function testDatabaseConnection($connection, $host, $port, $database, $username, $password)
    {
        $this->logInstallation("🔥 BEAST MODE: Testing database connection", 'info', [
            'connection' => $connection,
            'host' => $host,
            'port' => $port,
            'database' => $database
        ]);

        // BEAST MODE: Check if required PDO extension is available
        $requiredExtension = "pdo_{$connection}";
        if (!extension_loaded($requiredExtension)) {
            $this->logInstallation("❌ CRITICAL: Missing PHP extension: {$requiredExtension}", 'error');

            $fixInstructions = $this->getExtensionFixInstructions($requiredExtension);

            return [
                'success' => false,
                'error' => "🚨 CRITICAL: Missing PHP extension '{$requiredExtension}' - Database connections impossible!",
                'fix_instructions' => $fixInstructions,
                'technical_details' => "The {$requiredExtension} extension is required for {$connection} database connections. Without this extension, the application cannot connect to the database."
            ];
        }

        // BEAST MODE: Check if MySQL service is running
        if ($connection === 'mysql') {
            $serviceCheck = $this->checkMySQLService($host, $port);
            if (!$serviceCheck['success']) {
                return $serviceCheck;
            }
        }

        try {
            // 🔥 BEAST MODE: Try PDO connection first
            $this->logInstallation("🚀 Attempting PDO connection");

            // Test connection without database first
            $baseDsn = "{$connection}:host={$host};port={$port}";
            $basePdo = new PDO($baseDsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 10
            ]);

            // Check if database exists
            $databaseExists = $this->checkDatabaseExists($basePdo, $database, $connection);

            if (!$databaseExists) {
                // Try to create database
                try {
                    $this->createDatabase($basePdo, $database, $connection);
                    $this->logInstallation("✅ Database '{$database}' created successfully");
                } catch (Exception $e) {
                    $this->logInstallation("❌ Database creation failed: " . $e->getMessage(), 'error');
                    return [
                        'success' => false,
                        'error' => "Database '{$database}' does not exist and could not be created. Please create it manually or check permissions.",
                        'technical_details' => $e->getMessage()
                    ];
                }
            }

            // Now test connection with database
            $dsn = "{$connection}:host={$host};port={$port};dbname={$database}";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 10
            ]);

            // Test a simple query
            $pdo->query('SELECT 1');

            $this->logInstallation('🎉 BEAST MODE: PDO database connection test SUCCESSFUL!', 'info', [
                'host' => $host,
                'port' => $port,
                'database' => $database,
                'connection' => $connection
            ]);

            return ['success' => true, 'method' => 'PDO'];

        } catch (PDOException $e) {
            $this->logInstallation("❌ PDO connection failed, trying alternative methods", 'warning', [
                'error' => $e->getMessage()
            ]);

            // 🔥 BEAST MODE: Try alternative MySQLi connection
            if ($connection === 'mysql') {
                $alternativeResult = $this->tryAlternativeConnection($host, $port, $database, $username, $password);
                if ($alternativeResult['success']) {
                    return ['success' => true, 'method' => 'MySQLi', 'warning' => 'Using MySQLi instead of PDO. Consider enabling pdo_mysql extension.'];
                }
            }

            // If all methods fail, provide detailed error
        } catch (Exception $e) {
            $errorMessage = 'Database connection failed: ';

            if (strpos($e->getMessage(), 'could not find driver') !== false) {
                $errorMessage .= "Missing {$requiredExtension} PHP extension. Please enable it in your PHP configuration.";
            } elseif (strpos($e->getMessage(), 'Access denied') !== false) {
                $errorMessage .= 'Invalid username or password.';
            } elseif (strpos($e->getMessage(), 'Unknown database') !== false) {
                $errorMessage .= "Database '{$database}' does not exist. Please create it first.";
            } elseif (strpos($e->getMessage(), 'Connection refused') !== false) {
                $errorMessage .= 'Cannot connect to database server. Please check if the server is running and the host/port are correct.';
            } elseif (strpos($e->getMessage(), 'No such host') !== false) {
                $errorMessage .= 'Database host not found. Please check the hostname.';
            } else {
                $errorMessage .= $e->getMessage();
            }

            $this->logInstallation('Database connection test failed', 'error', [
                'error' => $e->getMessage(),
                'host' => $host,
                'port' => $port,
                'database' => $database
            ]);

            return [
                'success' => false,
                'error' => $errorMessage
            ];
        }
    }

    /**
     * Check if database exists
     */
    private function checkDatabaseExists($pdo, $database, $connection)
    {
        try {
            if ($connection === 'mysql') {
                $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
                $stmt->execute([$database]);
                return $stmt->rowCount() > 0;
            } elseif ($connection === 'pgsql') {
                $stmt = $pdo->prepare("SELECT datname FROM pg_database WHERE datname = ?");
                $stmt->execute([$database]);
                return $stmt->rowCount() > 0;
            }
            return false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Create database if it doesn't exist
     */
    private function createDatabase($pdo, $database, $connection)
    {
        if ($connection === 'mysql') {
            $pdo->exec("CREATE DATABASE `{$database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        } elseif ($connection === 'pgsql') {
            $pdo->exec("CREATE DATABASE \"{$database}\"");
        }
    }

    /**
     * Update .env file with new values
     */
    private function updateEnvFile($data)
    {
        $envPath = base_path('.env');

        if (!file_exists($envPath)) {
            // Copy from .env.example if .env doesn't exist
            if (file_exists(base_path('.env.example'))) {
                copy(base_path('.env.example'), $envPath);
            } else {
                throw new Exception('.env file does not exist and .env.example not found');
            }
        }

        $envContent = file_get_contents($envPath);

        foreach ($data as $key => $value) {
            // Escape special characters in value
            $escapedValue = $this->escapeEnvValue($value);

            // Check if key exists
            if (preg_match("/^{$key}=.*$/m", $envContent)) {
                $envContent = preg_replace("/^{$key}=.*$/m", "{$key}={$escapedValue}", $envContent);
            } else {
                // Add new key at the end
                $envContent .= "\n{$key}={$escapedValue}";
            }
        }

        if (!file_put_contents($envPath, $envContent)) {
            throw new Exception('Unable to write to .env file');
        }
    }

    /**
     * Escape environment variable value
     */
    private function escapeEnvValue($value)
    {
        if (empty($value)) {
            return '';
        }

        // If value contains spaces or special characters, wrap in quotes
        if (preg_match('/\s|[#"\'\\\\]/', $value)) {
            return '"' . str_replace('"', '\\"', $value) . '"';
        }

        return $value;
    }

    public function mail()
    {
        $this->logInstallation('Displaying mail configuration form');
        return view('install.mail');
    }

    public function saveMail(Request $request)
    {
        $this->logInstallation('Processing mail configuration', 'info', [
            'mailer' => $request->mail_mailer,
            'host' => $request->mail_host,
            'port' => $request->mail_port,
            'from' => $request->mail_from
        ]);

        $request->validate([
            'mail_mailer' => 'required|in:smtp,sendmail,mailgun,ses,postmark,log',
            'mail_host' => 'required_if:mail_mailer,smtp',
            'mail_port' => 'required_if:mail_mailer,smtp|nullable|numeric',
            'mail_user' => 'required_if:mail_mailer,smtp',
            'mail_pass' => 'nullable',
            'mail_encryption' => 'nullable|in:tls,ssl',
            'mail_from' => 'required|email',
            'mail_from_name' => 'required',
        ]);

        // Test mail configuration if SMTP
        if ($request->mail_mailer === 'smtp' && $request->test_mail) {
            $testResult = $this->testMailConnection(
                $request->mail_host,
                $request->mail_port,
                $request->mail_user,
                $request->mail_pass,
                $request->mail_encryption,
                $request->mail_from
            );

            if (!$testResult['success']) {
                $this->logInstallation('Mail configuration test failed', 'error', [
                    'error' => $testResult['error']
                ]);

                return redirect()->back()
                    ->withErrors(['mail_test' => $testResult['error']])
                    ->withInput();
            }
        }

        // Update .env file
        try {
            $mailData = [
                'MAIL_MAILER' => $request->mail_mailer,
                'MAIL_FROM_ADDRESS' => $request->mail_from,
                'MAIL_FROM_NAME' => $request->mail_from_name,
            ];

            if ($request->mail_mailer === 'smtp') {
                $mailData = array_merge($mailData, [
                    'MAIL_HOST' => $request->mail_host,
                    'MAIL_PORT' => $request->mail_port,
                    'MAIL_USERNAME' => $request->mail_user,
                    'MAIL_PASSWORD' => $request->mail_pass,
                    'MAIL_ENCRYPTION' => $request->mail_encryption,
                ]);
            }

            $this->updateEnvFile($mailData);

            $this->logInstallation('Mail configuration saved successfully');

            return redirect()->route('install.admin')
                ->with('success', 'Mail configuration saved successfully.');

        } catch (Exception $e) {
            $this->logInstallation('Failed to update mail configuration', 'error', [
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->withErrors(['env_update' => 'Failed to update configuration: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Test mail connection
     */
    private function testMailConnection($host, $port, $username, $password, $encryption, $fromEmail)
    {
        try {
            // Temporarily configure mail settings
            Config::set('mail.mailers.smtp.host', $host);
            Config::set('mail.mailers.smtp.port', $port);
            Config::set('mail.mailers.smtp.username', $username);
            Config::set('mail.mailers.smtp.password', $password);
            Config::set('mail.mailers.smtp.encryption', $encryption);
            Config::set('mail.from.address', $fromEmail);

            // Try to send a test email (we'll just test the connection)
            $transport = app('mail.manager')->createTransport([
                'transport' => 'smtp',
                'host' => $host,
                'port' => $port,
                'username' => $username,
                'password' => $password,
                'encryption' => $encryption,
            ]);

            // Test connection by trying to start the transport
            $transport->start();

            return ['success' => true];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Mail connection failed: ' . $e->getMessage()
            ];
        }
    }

    public function admin()
    {
        $this->logInstallation('Displaying admin account creation form');
        return view('install.admin');
    }

    public function saveAdmin(Request $request)
    {
        $this->logInstallation('Processing admin account creation', 'info', [
            'name' => $request->name,
            'email' => $request->email
        ]);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'password' => 'required|string|min:8|confirmed',
        ]);

        try {
            // 🔥 BEAST MODE: Fix cache driver before clearing caches
            $this->logInstallation('🚀 BEAST MODE: Starting database setup and admin creation');

            // CRITICAL: Switch to file cache during installation to avoid database dependency
            $this->logInstallation('Switching to file cache driver for installation');
            Config::set('cache.default', 'file');

            // Clear all caches with file driver
            $this->logInstallation('Clearing application caches (using file driver)');
            Artisan::call('config:clear');
            Artisan::call('cache:clear');
            Artisan::call('view:clear');
            Artisan::call('route:clear');

            // Generate APP_KEY if not exists
            if (empty(config('app.key'))) {
                $this->logInstallation('Generating application key');
                Artisan::call('key:generate', ['--force' => true]);

                // Reload config after key generation
                Artisan::call('config:clear');
            }

            // Test database connection before proceeding
            $this->logInstallation('Testing database connection before migration');
            DB::connection()->getPdo();
            $this->logInstallation('✅ Database connection confirmed');

            // 🔥 BEAST MODE: Enhanced migration execution with bulletproof error handling
            $this->logInstallation('🔍 Checking if database tables exist');
            $tablesExist = $this->checkIfTablesExist();

            if (!$tablesExist) {
                $this->logInstallation('🚀 BEAST MODE: Running database migrations');

                // Ensure we're using file cache during migration
                Config::set('cache.default', 'file');

                try {
                    // 🔥 BEAST MODE: Shared hosting compatible migration
                    $this->logInstallation('🚀 Running migrations (shared hosting compatible)');

                    // Try Artisan first, fallback to direct migration if it fails
                    try {
                        $migrationResult = Artisan::call('migrate', [
                            '--force' => true
                        ]);

                        // Get migration output for logging (fix array to string conversion)
                        $migrationOutput = Artisan::output();
                        $migrationOutputString = is_array($migrationOutput) ? implode("\n", $migrationOutput) : (string)$migrationOutput;

                        $this->logInstallation('Migration output', 'info', [
                            'exit_code' => $migrationResult,
                            'output' => $migrationOutputString
                        ]);

                        if ($migrationResult !== 0) {
                            throw new Exception("Artisan migration failed with exit code: {$migrationResult}");
                        }

                        $this->logInstallation('✅ Artisan migrations completed successfully');

                    } catch (Exception $artisanError) {
                        $this->logInstallation('⚠️  Artisan migration failed, trying direct migration', 'warning', [
                            'artisan_error' => $artisanError->getMessage()
                        ]);

                        // Fallback: Run migrations directly for shared hosting
                        $this->runMigrationsDirectly();
                        $this->logInstallation('✅ Direct migrations completed successfully');
                    }

                    // Verify critical tables were created
                    $this->verifyCriticalTables();

                } catch (Exception $e) {
                    $this->logInstallation('❌ Migration failed', 'error', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw new Exception("Database migration failed: " . $e->getMessage());
                }
            } else {
                $this->logInstallation('ℹ️  Database tables already exist, skipping migrations');
            }

            // 🔥 BEAST MODE: Enhanced seeder execution with bulletproof error handling
            $this->logInstallation('🔍 Checking if database seeders have been run');
            $seedersRun = $this->checkIfSeedersRun();

            if (!$seedersRun) {
                $this->logInstallation('🌱 BEAST MODE: Running database seeders');

                // Ensure we're still using file cache during seeding
                Config::set('cache.default', 'file');

                try {
                    // 🔥 BEAST MODE: Shared hosting compatible seeding
                    $this->logInstallation('🌱 Running seeders (shared hosting compatible)');

                    // Try Artisan first, fallback to direct seeding if it fails
                    try {
                        $seederResult = Artisan::call('db:seed', [
                            '--force' => true
                        ]);

                        // Get seeder output for logging (fix array to string conversion)
                        $seederOutput = Artisan::output();
                        $seederOutputString = is_array($seederOutput) ? implode("\n", $seederOutput) : (string)$seederOutput;

                        $this->logInstallation('Seeder output', 'info', [
                            'exit_code' => $seederResult,
                            'output' => $seederOutputString
                        ]);

                        if ($seederResult !== 0) {
                            throw new Exception("Artisan seeding failed with exit code: {$seederResult}");
                        }

                        $this->logInstallation('✅ Artisan seeders completed successfully');

                    } catch (Exception $artisanError) {
                        $this->logInstallation('⚠️  Artisan seeding failed, trying direct seeding', 'warning', [
                            'artisan_error' => $artisanError->getMessage()
                        ]);

                        // Fallback: Run seeders directly for shared hosting
                        $this->runSeedersDirectly();
                        $this->logInstallation('✅ Direct seeders completed successfully');
                    }

                    // Verify critical data was seeded
                    $this->verifyCriticalData();

                } catch (Exception $e) {
                    $this->logInstallation('❌ Seeding failed', 'error', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw new Exception("Database seeding failed: " . $e->getMessage());
                }
            } else {
                $this->logInstallation('ℹ️  Database seeders already run, skipping');
            }

            // 🔥 BEAST MODE: Enhanced admin user creation with bulletproof error handling
            $this->logInstallation('🔍 Checking for existing admin user');

            // Ensure we're still using file cache for user operations
            Config::set('cache.default', 'file');

            try {
                $existingAdmin = User::where('email', $request->email)->first();

                if ($existingAdmin) {
                    $this->logInstallation('👤 Admin user already exists, updating credentials');
                    $existingAdmin->update([
                        'name' => $request->name,
                        'password' => Hash::make($request->password),
                        'email_verified_at' => now(),
                    ]);
                    $user = $existingAdmin;
                    $this->logInstallation('✅ Existing admin user updated successfully');
                } else {
                    // Create admin user
                    $this->logInstallation('👤 Creating new admin user');
                    $user = User::create([
                        'name' => $request->name,
                        'email' => $request->email,
                        'password' => Hash::make($request->password),
                        'email_verified_at' => now(),
                    ]);
                    $this->logInstallation('✅ New admin user created successfully');
                }

                // Verify user was created/updated
                if (!$user || !$user->id) {
                    throw new Exception("Failed to create or update admin user");
                }

                // Ensure user has admin role with error handling
                $this->logInstallation('🔐 Assigning admin role to user');

                try {
                    if (!$user->hasRole(Role::ROLE_ADMIN)) {
                        $user->assignRole(Role::ROLE_ADMIN);
                        $this->logInstallation('✅ Admin role assigned to user');
                    } else {
                        $this->logInstallation('ℹ️  User already has admin role');
                    }

                    // Verify role assignment
                    if (!$user->hasRole(Role::ROLE_ADMIN)) {
                        throw new Exception("Failed to assign admin role to user");
                    }

                } catch (Exception $roleError) {
                    $this->logInstallation('❌ Role assignment failed', 'error', [
                        'error' => $roleError->getMessage()
                    ]);
                    throw new Exception("Failed to assign admin role: " . $roleError->getMessage());
                }

            } catch (Exception $userError) {
                $this->logInstallation('❌ User creation/update failed', 'error', [
                    'error' => $userError->getMessage()
                ]);
                throw new Exception("Failed to create/update admin user: " . $userError->getMessage());
            }

            $this->logInstallation('Admin account setup completed successfully', 'info', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);

            return redirect()->route('install.branding')
                ->with('success', 'Admin account created successfully.');

        } catch (Exception $e) {
            $this->logInstallation('❌ BEAST MODE: Admin account creation failed', 'error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => [
                    'name' => $request->name,
                    'email' => $request->email
                ]
            ]);

            // 🔥 BEAST MODE: Enhanced error response with recovery instructions
            $errorMessage = $e->getMessage();
            $recoveryInstructions = [];

            // Provide specific recovery instructions based on error type
            if (strpos($errorMessage, 'cache') !== false) {
                $recoveryInstructions[] = "Clear application cache: php artisan cache:clear";
                $recoveryInstructions[] = "Switch to file cache temporarily";
            }

            if (strpos($errorMessage, 'table') !== false && strpos($errorMessage, "doesn't exist") !== false) {
                $recoveryInstructions[] = "Run database migrations: php artisan migrate --force";
                $recoveryInstructions[] = "Check database connection settings";
            }

            if (strpos($errorMessage, 'role') !== false) {
                $recoveryInstructions[] = "Run database seeders: php artisan db:seed --force";
                $recoveryInstructions[] = "Verify roles and permissions tables exist";
            }

            return redirect()->back()
                ->withErrors([
                    'admin_creation' => 'Failed to create admin account: ' . $errorMessage,
                    'recovery_instructions' => $recoveryInstructions
                ])
                ->withInput();
        }
    }

    /**
     * 🔥 BEAST MODE: Check if database tables exist
     */
    private function checkIfTablesExist()
    {
        try {
            // Check if critical tables exist
            $criticalTables = ['users', 'roles', 'permissions', 'cache', 'sessions', 'migrations'];

            foreach ($criticalTables as $table) {
                if (!DB::getSchemaBuilder()->hasTable($table)) {
                    $this->logInstallation("Table '{$table}' does not exist");
                    return false;
                }
            }

            $this->logInstallation('All critical tables exist');
            return true;
        } catch (Exception $e) {
            $this->logInstallation('Error checking if tables exist', 'error', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 🔥 BEAST MODE: Check if seeders have been run
     */
    private function checkIfSeedersRun()
    {
        try {
            // Check if roles exist
            return DB::table('roles')->where('name', Role::ROLE_ADMIN)->exists();
        } catch (Exception $e) {
            $this->logInstallation('Error checking if seeders have been run', 'error', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 🔥 BEAST MODE: Verify critical tables were created during migration
     */
    private function verifyCriticalTables()
    {
        $this->logInstallation('🔍 Verifying critical tables were created');

        $criticalTables = [
            'users' => 'User management',
            'roles' => 'Role-based access control',
            'permissions' => 'Permission system',
            'cache' => 'Application caching',
            'sessions' => 'Session management',
            'migrations' => 'Migration tracking',
            'password_reset_tokens' => 'Password reset functionality',
            'failed_jobs' => 'Job queue management'
        ];

        $missingTables = [];

        foreach ($criticalTables as $table => $description) {
            try {
                if (!DB::getSchemaBuilder()->hasTable($table)) {
                    $missingTables[] = "{$table} ({$description})";
                    $this->logInstallation("❌ Critical table missing: {$table}", 'error');
                } else {
                    $this->logInstallation("✅ Table verified: {$table}");
                }
            } catch (Exception $e) {
                $missingTables[] = "{$table} (verification failed: {$e->getMessage()})";
                $this->logInstallation("❌ Error verifying table {$table}", 'error', [
                    'error' => $e->getMessage()
                ]);
            }
        }

        if (!empty($missingTables)) {
            throw new Exception("Critical tables are missing after migration: " . implode(', ', $missingTables));
        }

        $this->logInstallation('✅ All critical tables verified successfully');
    }

    /**
     * 🔥 BEAST MODE: Verify critical data was seeded properly
     */
    private function verifyCriticalData()
    {
        $this->logInstallation('🔍 Verifying critical data was seeded');

        try {
            // Check if roles exist
            $adminRoleExists = DB::table('roles')->where('name', Role::ROLE_ADMIN)->exists();
            if (!$adminRoleExists) {
                throw new Exception("Admin role not found in database");
            }
            $this->logInstallation('✅ Admin role verified');

            // Check if permissions exist
            $permissionCount = DB::table('permissions')->count();
            if ($permissionCount === 0) {
                throw new Exception("No permissions found in database");
            }
            $this->logInstallation("✅ Permissions verified ({$permissionCount} permissions found)");

            $this->logInstallation('✅ All critical data verified successfully');

        } catch (Exception $e) {
            $this->logInstallation('❌ Critical data verification failed', 'error', [
                'error' => $e->getMessage()
            ]);
            throw new Exception("Critical data verification failed: " . $e->getMessage());
        }
    }

    /**
     * 🔥 BEAST MODE: Run migrations directly for shared hosting compatibility
     */
    private function runMigrationsDirectly()
    {
        $this->logInstallation('🚀 Running migrations directly (shared hosting mode)');

        try {
            // Get migration files
            $migrationPath = database_path('migrations');
            $migrationFiles = glob($migrationPath . '/*.php');
            sort($migrationFiles);

            $this->logInstallation("Found " . count($migrationFiles) . " migration files");

            // Create migrations table if it doesn't exist
            if (!Schema::hasTable('migrations')) {
                Schema::create('migrations', function ($table) {
                    $table->increments('id');
                    $table->string('migration');
                    $table->integer('batch');
                });
                $this->logInstallation('✅ Migrations table created');
            }

            // Get current batch number
            $batch = DB::table('migrations')->max('batch') + 1;

            foreach ($migrationFiles as $file) {
                $migrationName = basename($file, '.php');

                // Check if migration already ran
                $exists = DB::table('migrations')->where('migration', $migrationName)->exists();
                if ($exists) {
                    $this->logInstallation("⏭️  Skipping already run migration: {$migrationName}");
                    continue;
                }

                try {
                    // Include and run the migration
                    $migration = include $file;
                    $migration->up();

                    // Record the migration
                    DB::table('migrations')->insert([
                        'migration' => $migrationName,
                        'batch' => $batch
                    ]);

                    $this->logInstallation("✅ Migration completed: {$migrationName}");

                } catch (Exception $e) {
                    $this->logInstallation("❌ Migration failed: {$migrationName}", 'error', [
                        'error' => $e->getMessage()
                    ]);
                    throw new Exception("Migration {$migrationName} failed: " . $e->getMessage());
                }
            }

            $this->logInstallation('✅ All migrations completed successfully');

        } catch (Exception $e) {
            $this->logInstallation('❌ Direct migration failed', 'error', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 🔥 BEAST MODE: Run seeders directly for shared hosting compatibility
     */
    private function runSeedersDirectly()
    {
        $this->logInstallation('🌱 Running seeders directly (shared hosting mode)');

        try {
            // Run specific seeders in order
            $seeders = [
                'RoleSeeder',
                'PermissionSeeder',
                'DatabaseSeeder'
            ];

            foreach ($seeders as $seederClass) {
                try {
                    $seederPath = database_path("seeders/{$seederClass}.php");

                    if (file_exists($seederPath)) {
                        // Include the seeder file
                        require_once $seederPath;

                        // Check if class exists
                        if (class_exists($seederClass)) {
                            $seeder = new $seederClass();
                            $seeder->run();
                            $this->logInstallation("✅ Seeder completed: {$seederClass}");
                        } else {
                            $this->logInstallation("⚠️  Seeder class not found: {$seederClass}");
                        }
                    } else {
                        $this->logInstallation("⚠️  Seeder file not found: {$seederClass}");
                    }

                } catch (Exception $e) {
                    $this->logInstallation("❌ Seeder failed: {$seederClass}", 'error', [
                        'error' => $e->getMessage()
                    ]);
                    // Continue with other seeders
                }
            }

            // Manually create essential roles if they don't exist
            $this->ensureEssentialRoles();

            $this->logInstallation('✅ All seeders completed successfully');

        } catch (Exception $e) {
            $this->logInstallation('❌ Direct seeding failed', 'error', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 🔥 BEAST MODE: Ensure essential roles exist (fallback for shared hosting)
     */
    private function ensureEssentialRoles()
    {
        $this->logInstallation('🔐 Ensuring essential roles exist');

        try {
            // Check if roles table exists
            if (!Schema::hasTable('roles')) {
                throw new Exception("Roles table does not exist");
            }

            // Essential roles
            $essentialRoles = [
                ['name' => 'admin', 'guard_name' => 'web'],
                ['name' => 'user', 'guard_name' => 'web']
            ];

            foreach ($essentialRoles as $role) {
                $exists = DB::table('roles')->where('name', $role['name'])->exists();
                if (!$exists) {
                    DB::table('roles')->insert(array_merge($role, [
                        'created_at' => now(),
                        'updated_at' => now()
                    ]));
                    $this->logInstallation("✅ Created role: {$role['name']}");
                } else {
                    $this->logInstallation("ℹ️  Role already exists: {$role['name']}");
                }
            }

            // Essential permissions
            $essentialPermissions = [
                ['name' => 'manage users', 'guard_name' => 'web'],
                ['name' => 'manage roles', 'guard_name' => 'web'],
                ['name' => 'manage invoices', 'guard_name' => 'web']
            ];

            if (Schema::hasTable('permissions')) {
                foreach ($essentialPermissions as $permission) {
                    $exists = DB::table('permissions')->where('name', $permission['name'])->exists();
                    if (!$exists) {
                        DB::table('permissions')->insert(array_merge($permission, [
                            'created_at' => now(),
                            'updated_at' => now()
                        ]));
                        $this->logInstallation("✅ Created permission: {$permission['name']}");
                    }
                }
            }

            $this->logInstallation('✅ Essential roles and permissions ensured');

        } catch (Exception $e) {
            $this->logInstallation('❌ Failed to ensure essential roles', 'error', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    public function branding()
    {
        $this->logInstallation('Displaying branding configuration form');
        return view('install.branding');
    }

    public function saveBranding(Request $request)
    {
        $this->logInstallation('Processing branding configuration', 'info', [
            'company_name' => $request->company_name,
            'currency' => $request->company_currency
        ]);

        $request->validate([
            'company_name' => 'required|string|max:255',
            'company_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'company_currency' => 'required|string|max:10',
            'company_address' => 'nullable|string|max:500',
            'company_phone' => 'nullable|string|max:20',
            'company_email' => 'nullable|email|max:255',
        ]);

        try {
            $brandingData = [
                'APP_NAME' => '"' . $request->company_name . '"',
                'CURRENCY_SYMBOL' => $request->company_currency,
            ];

            // Handle logo upload
            if ($request->hasFile('company_logo')) {
                $this->logInstallation('Processing company logo upload');

                // Ensure public storage link exists
                if (!file_exists(public_path('storage'))) {
                    Artisan::call('storage:link');
                }

                $logoPath = $request->file('company_logo')->store('logos', 'public');
                $brandingData['APP_LOGO'] = $logoPath;

                $this->logInstallation('Company logo uploaded', 'info', [
                    'path' => $logoPath
                ]);
            }

            // Add optional company details
            if ($request->company_address) {
                $brandingData['COMPANY_ADDRESS'] = '"' . $request->company_address . '"';
            }
            if ($request->company_phone) {
                $brandingData['COMPANY_PHONE'] = $request->company_phone;
            }
            if ($request->company_email) {
                $brandingData['COMPANY_EMAIL'] = $request->company_email;
            }

            $this->updateEnvFile($brandingData);

            $this->logInstallation('Branding configuration saved successfully');

            return redirect()->route('install.summary')
                ->with('success', 'Branding configuration saved successfully.');

        } catch (Exception $e) {
            $this->logInstallation('Failed to save branding configuration', 'error', [
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->withErrors(['branding_save' => 'Failed to save branding: ' . $e->getMessage()])
                ->withInput();
        }
    }

    public function summary()
    {
        $this->logInstallation('Displaying installation summary');

        // Gather configuration summary
        $summary = [
            'database' => [
                'connection' => config('database.default'),
                'host' => config('database.connections.' . config('database.default') . '.host'),
                'database' => config('database.connections.' . config('database.default') . '.database'),
                'username' => config('database.connections.' . config('database.default') . '.username'),
            ],
            'mail' => [
                'mailer' => config('mail.default'),
                'host' => config('mail.mailers.smtp.host'),
                'port' => config('mail.mailers.smtp.port'),
                'from_address' => config('mail.from.address'),
                'from_name' => config('mail.from.name'),
            ],
            'app' => [
                'name' => config('app.name'),
                'url' => config('app.url'),
                'timezone' => config('app.timezone'),
                'locale' => config('app.locale'),
            ]
        ];

        return view('install.summary', compact('summary'));
    }

    public function finalize()
    {
        $this->logInstallation('Starting installation finalization');

        try {
            // Clear and cache configurations
            $this->logInstallation('Clearing and caching configurations');

            Artisan::call('config:clear');
            Artisan::call('cache:clear');
            Artisan::call('view:clear');
            Artisan::call('route:clear');

            // Cache optimizations for production
            if (config('app.env') === 'production') {
                Artisan::call('config:cache');
                Artisan::call('route:cache');
                Artisan::call('view:cache');
                Artisan::call('event:cache');
            }

            // Create storage symlink if it doesn't exist
            if (!file_exists(public_path('storage'))) {
                $this->logInstallation('Creating storage symlink');
                Artisan::call('storage:link');
            }

            // Set proper permissions
            $this->setProperPermissions();

            // Mark installation as complete
            $installationData = [
                'installed_at' => now()->toISOString(),
                'version' => config('app.version', '1.0.0'),
                'php_version' => phpversion(),
                'laravel_version' => app()->version(),
            ];

            file_put_contents(
                storage_path('installed'),
                json_encode($installationData, JSON_PRETTY_PRINT)
            );

            $this->logInstallation('Installation completed successfully', 'info', $installationData);

            return redirect()->route('install.success')
                ->with('success', 'Installation completed successfully!');

        } catch (Exception $e) {
            $this->logInstallation('Installation finalization failed', 'error', [
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->withErrors(['finalization' => 'Installation finalization failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Set proper file permissions
     */
    private function setProperPermissions()
    {
        $directories = [
            storage_path(),
            storage_path('app'),
            storage_path('framework'),
            storage_path('logs'),
            base_path('bootstrap/cache'),
        ];

        foreach ($directories as $directory) {
            if (file_exists($directory)) {
                chmod($directory, 0755);
                $this->logInstallation("Set permissions for: {$directory}");
            }
        }
    }

    public function success()
    {
        $this->logInstallation('Installation success page displayed');

        $installationInfo = null;
        if (file_exists(storage_path('installed'))) {
            $installationInfo = json_decode(file_get_contents(storage_path('installed')), true);
        }

        return view('install.success', compact('installationInfo'));
    }

    /**
     * AJAX endpoint to test database connection
     */
    public function testDatabase(Request $request)
    {
        $request->validate([
            'db_connection' => 'required|in:mysql,pgsql',
            'db_host' => 'required',
            'db_port' => 'required|numeric',
            'db_name' => 'required',
            'db_user' => 'required',
            'db_pass' => 'nullable',
        ]);

        $result = $this->testDatabaseConnection(
            $request->db_connection,
            $request->db_host,
            $request->db_port,
            $request->db_name,
            $request->db_user,
            $request->db_pass
        );

        return response()->json($result);
    }

    /**
     * AJAX endpoint to test mail configuration
     */
    public function testMail(Request $request)
    {
        $request->validate([
            'mail_host' => 'required',
            'mail_port' => 'required|numeric',
            'mail_user' => 'required',
            'mail_pass' => 'nullable',
            'mail_encryption' => 'nullable|in:tls,ssl',
            'mail_from' => 'required|email',
        ]);

        $result = $this->testMailConnection(
            $request->mail_host,
            $request->mail_port,
            $request->mail_user,
            $request->mail_pass,
            $request->mail_encryption,
            $request->mail_from
        );

        return response()->json($result);
    }

    /**
     * Rollback installation in case of failure
     */
    public function rollback()
    {
        try {
            $this->logInstallation('Starting installation rollback');

            // Remove installed flag
            if (file_exists(storage_path('installed'))) {
                unlink(storage_path('installed'));
                $this->logInstallation('Removed installation flag');
            }

            // Clear all caches
            Artisan::call('config:clear');
            Artisan::call('cache:clear');
            Artisan::call('view:clear');
            Artisan::call('route:clear');

            // Reset .env to example
            if (file_exists(base_path('.env.example'))) {
                copy(base_path('.env.example'), base_path('.env'));
                $this->logInstallation('Reset .env file to example');
            }

            $this->logInstallation('Installation rollback completed');

            return redirect()->route('install.index')
                ->with('success', 'Installation has been reset. You can start over.');

        } catch (Exception $e) {
            $this->logInstallation('Rollback failed', 'error', [
                'error' => $e->getMessage()
            ]);

            return redirect()->route('install.index')
                ->with('error', 'Rollback failed: ' . $e->getMessage());
        }
    }

    /**
     * Get installation status and progress
     */
    public function status()
    {
        $status = [
            'installed' => $this->isInstalled(),
            'php_version' => phpversion(),
            'extensions' => [],
            'permissions' => [],
            'database' => null,
            'progress' => session('installation_step', 0),
            'completed_steps' => session('installation_completed_steps', [])
        ];

        // Check extensions
        $requiredExtensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'tokenizer', 'xml'];
        foreach ($requiredExtensions as $ext) {
            $status['extensions'][$ext] = extension_loaded($ext);
        }

        // Check permissions
        $requiredPaths = ['storage', 'bootstrap/cache'];
        foreach ($requiredPaths as $path) {
            $fullPath = base_path($path);
            $status['permissions'][$path] = is_writable($fullPath);
        }

        // Check database if configured
        try {
            if (config('database.connections.mysql.host')) {
                DB::connection()->getPdo();
                $status['database'] = 'connected';
            }
        } catch (Exception $e) {
            $status['database'] = 'failed';
        }

        return response()->json($status);
    }

    /**
     * Handle installation errors with proper logging and user feedback
     */
    private function handleInstallationError(Exception $e, $step, $context = [])
    {
        $errorData = [
            'step' => $step,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'context' => $context
        ];

        $this->logInstallation("Installation error in step: {$step}", 'error', $errorData);

        // Determine user-friendly error message
        $userMessage = $this->getUserFriendlyErrorMessage($e, $step);

        return [
            'success' => false,
            'error' => $userMessage,
            'technical_details' => config('app.debug') ? $e->getMessage() : null
        ];
    }

    /**
     * Convert technical errors to user-friendly messages
     */
    private function getUserFriendlyErrorMessage(Exception $e, $step)
    {
        $message = $e->getMessage();

        // Database related errors
        if (strpos($message, 'could not find driver') !== false) {
            return 'Database driver not found. Please ensure the PDO MySQL extension is installed and enabled.';
        }

        if (strpos($message, 'Access denied') !== false) {
            return 'Database access denied. Please check your database username and password.';
        }

        if (strpos($message, 'Connection refused') !== false) {
            return 'Cannot connect to database server. Please ensure the database server is running.';
        }

        // File permission errors
        if (strpos($message, 'Permission denied') !== false) {
            return 'File permission error. Please ensure the web server has write access to storage and bootstrap/cache directories.';
        }

        // Migration errors
        if (strpos($message, 'Migration') !== false) {
            return 'Database migration failed. Please check your database connection and permissions.';
        }

        // Default to original message for other errors
        return $message;
    }

    /**
     * 🔥 BEAST MODE: Get extension fix instructions
     */
    private function getExtensionFixInstructions($extension)
    {
        $instructions = [
            'xampp' => [],
            'wamp' => [],
            'linux' => [],
            'general' => []
        ];

        if ($extension === 'pdo_mysql') {
            $instructions['xampp'] = [
                '1. Open XAMPP Control Panel',
                '2. Click "Config" next to Apache',
                '3. Select "PHP (php.ini)"',
                '4. Find and uncomment: ;extension=pdo_mysql',
                '5. Remove the semicolon: extension=pdo_mysql',
                '6. Save the file',
                '7. Restart Apache server',
                '8. Refresh this page'
            ];

            $instructions['wamp'] = [
                '1. Left-click WAMP icon in system tray',
                '2. Go to PHP > PHP Extensions',
                '3. Check "pdo_mysql" if unchecked',
                '4. Restart all services',
                '5. Refresh this page'
            ];

            $instructions['linux'] = [
                'Ubuntu/Debian: sudo apt-get install php-mysql',
                'CentOS/RHEL: sudo yum install php-mysql',
                'Or: sudo dnf install php-mysqlnd',
                'Then restart web server: sudo systemctl restart apache2'
            ];

            $instructions['general'] = [
                '1. Locate your php.ini file',
                '2. Find the line: ;extension=pdo_mysql',
                '3. Remove the semicolon to uncomment it',
                '4. Save the file',
                '5. Restart your web server',
                '6. Verify with: php -m | grep pdo_mysql'
            ];
        }

        return $instructions;
    }

    /**
     * 🔥 BEAST MODE: Check MySQL service availability
     */
    private function checkMySQLService($host, $port)
    {
        $this->logInstallation("🔍 Checking MySQL service on {$host}:{$port}");

        $connection = @fsockopen($host, $port, $errno, $errstr, 5);

        if (!$connection) {
            $this->logInstallation("❌ MySQL service check failed: {$errstr} ({$errno})", 'error');

            return [
                'success' => false,
                'error' => "🚨 MySQL service is not running or not accessible on {$host}:{$port}",
                'fix_instructions' => [
                    'xampp' => [
                        '1. Open XAMPP Control Panel',
                        '2. Start MySQL service if not running',
                        '3. Check if port 3306 is not blocked by firewall',
                        '4. Try again'
                    ],
                    'general' => [
                        '1. Ensure MySQL/MariaDB service is running',
                        '2. Check if the port is correct (usually 3306)',
                        '3. Verify firewall settings',
                        '4. Check if another service is using the port'
                    ]
                ]
            ];
        }

        fclose($connection);
        $this->logInstallation("✅ MySQL service is accessible");

        return ['success' => true];
    }

    /**
     * 🔥 BEAST MODE: Alternative database connection using mysqli
     */
    private function tryAlternativeConnection($host, $port, $database, $username, $password)
    {
        $this->logInstallation("🔄 Trying alternative MySQLi connection");

        if (!extension_loaded('mysqli')) {
            return [
                'success' => false,
                'error' => 'Both PDO MySQL and MySQLi extensions are missing'
            ];
        }

        try {
            $mysqli = new mysqli($host, $username, $password, '', $port);

            if ($mysqli->connect_error) {
                throw new Exception($mysqli->connect_error);
            }

            // Try to select/create database
            if (!$mysqli->select_db($database)) {
                $mysqli->query("CREATE DATABASE `{$database}`");
                if (!$mysqli->select_db($database)) {
                    throw new Exception("Cannot create or access database: {$database}");
                }
            }

            $mysqli->close();

            $this->logInstallation("✅ Alternative MySQLi connection successful");

            return ['success' => true];

        } catch (Exception $e) {
            $this->logInstallation("❌ Alternative MySQLi connection failed: " . $e->getMessage(), 'error');

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
