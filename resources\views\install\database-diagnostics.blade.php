<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 BEAST MODE: Database Connection Diagnostics</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-12 px-4">
        <div class="max-w-6xl mx-auto">
            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                <!-- Header -->
                <div class="bg-gradient-to-r from-red-600 to-red-700 px-8 py-6">
                    <h1 class="text-2xl font-bold text-white">🔥 BEAST MODE: Database Connection Diagnostics</h1>
                    <p class="text-red-100 mt-1">Diagnose and fix database connection issues</p>
                </div>

                <!-- Content -->
                <div class="px-8 py-6" x-data="databaseDiagnostics()">
                    <!-- Current Environment Detection -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">🌐 Environment Detection</h2>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <strong>Server Software:</strong> {{ $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' }}
                                </div>
                                <div>
                                    <strong>PHP Version:</strong> {{ phpversion() }}
                                </div>
                                <div>
                                    <strong>Document Root:</strong> {{ $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown' }}
                                </div>
                                <div>
                                    <strong>HTTP Host:</strong> {{ $_SERVER['HTTP_HOST'] ?? 'Unknown' }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Database Configuration Test -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">🗄️ Database Configuration Test</h2>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Database Host</label>
                                    <input type="text" x-model="dbConfig.host" placeholder="localhost" 
                                           class="w-full border rounded px-3 py-2 text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Database Port</label>
                                    <input type="text" x-model="dbConfig.port" placeholder="3306" 
                                           class="w-full border rounded px-3 py-2 text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Database Name</label>
                                    <input type="text" x-model="dbConfig.database" placeholder="invoice_db" 
                                           class="w-full border rounded px-3 py-2 text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                                    <input type="text" x-model="dbConfig.username" placeholder="username" 
                                           class="w-full border rounded px-3 py-2 text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                                    <input type="password" x-model="dbConfig.password" placeholder="password" 
                                           class="w-full border rounded px-3 py-2 text-sm">
                                </div>
                                <div class="flex items-end">
                                    <button @click="testDatabaseConnection()" 
                                            :disabled="loading"
                                            class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50">
                                        <i class="fas fa-database mr-2" :class="{'fa-spin': loading}"></i>
                                        Test Connection
                                    </button>
                                </div>
                            </div>
                            <div id="db-test-result" class="mt-4 text-sm"></div>
                        </div>
                    </div>

                    <!-- Environment-Specific Guides -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">📋 Environment-Specific Configuration</h2>
                        
                        <!-- GoDaddy Configuration -->
                        <div class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h3 class="font-medium text-blue-800 mb-3">🌐 GoDaddy Shared Hosting Configuration</h3>
                            <div class="text-sm text-blue-700 space-y-2">
                                <div><strong>Typical Host:</strong> localhost</div>
                                <div><strong>Username Format:</strong> cpanel_username_dbuser (e.g., mysite_invoice)</div>
                                <div><strong>Database Name:</strong> cpanel_username_dbname (e.g., mysite_invoicedb)</div>
                                <div><strong>Port:</strong> 3306 (default)</div>
                                <div class="mt-3 p-3 bg-blue-100 rounded">
                                    <strong>⚠️ Common Issues:</strong><br>
                                    • Username format must be: cpanel_username_dbuser<br>
                                    • Database name must be: cpanel_username_dbname<br>
                                    • Check cPanel for exact credentials<br>
                                    • Ensure database user has all privileges
                                </div>
                            </div>
                        </div>

                        <!-- XAMPP Configuration -->
                        <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
                            <h3 class="font-medium text-green-800 mb-3">💻 XAMPP Local Configuration</h3>
                            <div class="text-sm text-green-700 space-y-2">
                                <div><strong>Host:</strong> localhost or 127.0.0.1</div>
                                <div><strong>Username:</strong> root</div>
                                <div><strong>Password:</strong> (empty by default)</div>
                                <div><strong>Port:</strong> 3306</div>
                                <div class="mt-3 p-3 bg-green-100 rounded">
                                    <strong>⚠️ Common Issues:</strong><br>
                                    • MySQL service not started in XAMPP Control Panel<br>
                                    • Port 3306 already in use<br>
                                    • Document root not pointing to Laravel public folder<br>
                                    • URL rewrite not enabled in Apache
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- .env File Generator -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">⚙️ .env Configuration Generator</h2>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Environment Type</label>
                                <select x-model="envType" @change="updateEnvTemplate()" 
                                        class="w-full border rounded px-3 py-2 text-sm">
                                    <option value="godaddy">GoDaddy Shared Hosting</option>
                                    <option value="xampp">XAMPP Local</option>
                                    <option value="custom">Custom</option>
                                </select>
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">.env Database Configuration</label>
                                <textarea x-model="envTemplate" 
                                          class="w-full border rounded px-3 py-2 text-sm font-mono h-32"
                                          placeholder="Database configuration will appear here..."></textarea>
                            </div>
                            
                            <button @click="copyEnvConfig()" 
                                    class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                                <i class="fas fa-copy mr-2"></i>
                                Copy Configuration
                            </button>
                        </div>
                    </div>

                    <!-- Quick Fixes -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">🔧 Quick Fixes</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <button @click="generateGoDaddyConfig()" 
                                    class="bg-blue-600 text-white px-4 py-3 rounded hover:bg-blue-700">
                                <i class="fas fa-cloud mr-2"></i>
                                Generate GoDaddy Config
                            </button>
                            
                            <button @click="generateXAMPPConfig()" 
                                    class="bg-green-600 text-white px-4 py-3 rounded hover:bg-green-700">
                                <i class="fas fa-desktop mr-2"></i>
                                Generate XAMPP Config
                            </button>
                            
                            <button @click="testCurrentConfig()" 
                                    class="bg-purple-600 text-white px-4 py-3 rounded hover:bg-purple-700">
                                <i class="fas fa-vial mr-2"></i>
                                Test Current Config
                            </button>
                            
                            <button @click="downloadEnvFile()" 
                                    class="bg-orange-600 text-white px-4 py-3 rounded hover:bg-orange-700">
                                <i class="fas fa-download mr-2"></i>
                                Download .env File
                            </button>
                        </div>
                    </div>

                    <!-- Additional Tools -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">🔗 Additional Tools</h2>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <a href="/install-diagnostics"
                               class="bg-blue-100 border border-blue-300 rounded-lg p-4 text-center hover:bg-blue-200">
                                <i class="fas fa-cogs text-blue-600 mb-2"></i>
                                <div class="font-medium text-blue-800">Installation Diagnostics</div>
                            </a>

                            <a href="/post-installation-fixes"
                               class="bg-green-100 border border-green-300 rounded-lg p-4 text-center hover:bg-green-200">
                                <i class="fas fa-wrench text-green-600 mb-2"></i>
                                <div class="font-medium text-green-800">Post-Installation Fixes</div>
                            </a>

                            <a href="/auth-diagnostics"
                               class="bg-purple-100 border border-purple-300 rounded-lg p-4 text-center hover:bg-purple-200">
                                <i class="fas fa-user-shield text-purple-600 mb-2"></i>
                                <div class="font-medium text-purple-800">Auth Diagnostics</div>
                            </a>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <div class="flex items-center justify-between pt-6 border-t">
                        <a href="/install"
                           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Installation
                        </a>

                        <a href="/install/web-migration"
                           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                            Web Migration Tool
                            <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        function databaseDiagnostics() {
            return {
                loading: false,
                envType: 'godaddy',
                envTemplate: '',
                dbConfig: {
                    host: 'localhost',
                    port: '3306',
                    database: '',
                    username: '',
                    password: ''
                },
                
                init() {
                    this.updateEnvTemplate();
                },
                
                updateEnvTemplate() {
                    if (this.envType === 'godaddy') {
                        this.envTemplate = `DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=cpanel_username_dbname
DB_USERNAME=cpanel_username_dbuser
DB_PASSWORD=your_database_password

# Replace 'cpanel_username' with your actual cPanel username
# Example: if your cPanel username is 'mysite123'
# DB_DATABASE=mysite123_invoicedb
# DB_USERNAME=mysite123_invoice`;
                    } else if (this.envType === 'xampp') {
                        this.envTemplate = `DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=invoice_management
DB_USERNAME=root
DB_PASSWORD=

# For XAMPP, password is usually empty
# Make sure MySQL is running in XAMPP Control Panel`;
                    } else {
                        this.envTemplate = `DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_username
DB_PASSWORD=your_password`;
                    }
                },
                
                async testDatabaseConnection() {
                    this.loading = true;
                    const resultDiv = document.getElementById('db-test-result');
                    resultDiv.innerHTML = '<span class="text-blue-600">🔄 Testing database connection...</span>';
                    
                    try {
                        const response = await fetch('/test-database-connection', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                            },
                            body: JSON.stringify(this.dbConfig)
                        });
                        
                        const data = await response.json();
                        
                        if (data.success) {
                            resultDiv.innerHTML = `<span class="text-green-600">✅ ${data.message}</span>`;
                        } else {
                            resultDiv.innerHTML = `<span class="text-red-600">❌ ${data.error}</span>`;
                        }
                    } catch (error) {
                        resultDiv.innerHTML = `<span class="text-red-600">❌ Connection test failed: ${error.message}</span>`;
                    } finally {
                        this.loading = false;
                    }
                },
                
                generateGoDaddyConfig() {
                    this.envType = 'godaddy';
                    this.updateEnvTemplate();
                    alert('GoDaddy configuration generated! Please update the database credentials with your actual cPanel information.');
                },
                
                generateXAMPPConfig() {
                    this.envType = 'xampp';
                    this.updateEnvTemplate();
                    alert('XAMPP configuration generated! Make sure MySQL is running in XAMPP Control Panel.');
                },
                
                async testCurrentConfig() {
                    try {
                        const response = await fetch('/test-current-database');
                        const data = await response.json();
                        
                        if (data.success) {
                            alert('✅ Current database configuration is working!');
                        } else {
                            alert('❌ Current database configuration failed: ' + data.error);
                        }
                    } catch (error) {
                        alert('❌ Failed to test current configuration: ' + error.message);
                    }
                },
                
                copyEnvConfig() {
                    navigator.clipboard.writeText(this.envTemplate).then(() => {
                        alert('✅ Configuration copied to clipboard!');
                    }).catch(() => {
                        alert('❌ Failed to copy to clipboard. Please copy manually.');
                    });
                },
                
                downloadEnvFile() {
                    const blob = new Blob([this.envTemplate], { type: 'text/plain' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = '.env';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                }
            }
        }
    </script>
</body>
</html>
