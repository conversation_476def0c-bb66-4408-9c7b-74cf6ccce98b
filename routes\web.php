<?php

use App\Models\Role;
use App\Livewire\ResetClientPassword;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\QuoteController;
use App\Http\Controllers\Client as Client;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\AdminPaymentController;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;

Route::get('/', function () {
    if (!Auth::check()) {
        return redirect()->route('filament.admin.auth.login');
    }
    if (Auth::user()->hasRole(Role::ROLE_ADMIN)) {
        return redirect()->route('filament.admin.pages.dashboard');
    }

    if (Auth::user()->hasRole(Role::ROLE_CLIENT)) {
        return redirect()->route('filament.client.pages.dashboard');
    }

    // Default fallback if user has no role
    return redirect()->route('filament.admin.auth.login');
});









Route::get('quote/{quoteId}', [QuoteController::class, 'showPublicQuote'])->name('quote-show-url');
Route::get('invoice/{invoiceId}', [InvoiceController::class, 'showPublicInvoice'])->name('invoice-show-url');
Route::get(
    'quote-pdf/{quote}',
    [QuoteController::class, 'getPublicQuotePdf']
)->name('public-view-quote.pdf');

Route::get(
    'invoice/{invoiceId}/payment',
    [InvoiceController::class, 'showPublicPayment']
)->name('invoices.public-payment');


//? for Admin
Route::middleware(['auth', 'role:admin'])->group(function () {
    //? invoice
    Route::prefix('invoices')->name('invoices.')->group(function () {
        Route::get('/{invoice}/pdf', [InvoiceController::class, 'convertToPdf'])->name('pdf');
    });
    Route::get('invoices-pdf', [InvoiceController::class, 'exportInvoicesPdf'])->name('admin.invoices.pdf');
    Route::get('/invoices-excel', [InvoiceController::class, 'exportInvoicesExcel'])->name('admin.invoicesExcel');
    //? quote
    Route::get('/quotes-excel', [QuoteController::class, 'exportQuotesExcel'])->name('admin.quotesExcel');
    Route::get('quotes-pdf', [QuoteController::class, 'exportQuotesPdf'])->name('admin.quotes.pdf');
    Route::get('quotes/{quote}/pdf', [QuoteController::class, 'convertToPdf'])->name('quotes.pdf');
});
Route::get('/client-onboard/{id}', ResetClientPassword::class)->name('client.password.reset')->middleware('setLanguageFront');

//? for Client
Route::middleware(['auth', 'role:client'])->group(function () {
    //? invoice
    Route::get('/invoice-excel', [InvoiceController::class, 'clientExportInvoicesExcel'])->name('client.invoicesExcel');
    Route::get('invoice-pdf', [InvoiceController::class, 'clientExportInvoicesPdf'])->name('client.invoices.pdf');
    Route::get('invoice/{invoice}/pdf', [InvoiceController::class, 'clientConvertToPdf'])->name('clients.invoices.pdf');
    //? quote
    Route::get('/quote-excel', [QuoteController::class, 'clientExportQuotesExcel'])->name('client.quotesExcel');
    Route::get('quote-pdf', [QuoteController::class, 'clientExportQuotesPdf'])->name('client.export.quotes.pdf');
    Route::get('quote/{quote}/pdf', [QuoteController::class, 'clientConvertToPdf'])->name('client.quotes.pdf');

    // transactions
    Route::get('client-transactions-excel', [client\PaymentController::class, 'exportTransactionsExcel'])->name('client.transactionsExcel');
    Route::get('client-transactions-pdf', [client\PaymentController::class, 'exportTransactionsPdf'])->name('client.export.transactions.pdf');
});

Route::get('invoice-pdf/{invoice}', [InvoiceController::class, 'getPublicInvoicePdf'])->name('public-view-invoice.pdf');
Route::get('transactions-attachment/{id}', [PaymentController::class, 'downloadAttachment'])->name('transaction.attachment');

// export payments excel admin route
Route::get(
    'admin-payments-excel',
    [AdminPaymentController::class, 'exportAdminPaymentsExcel']
)->name('admin.paymentsExcel');

// export payments pdf admin route
Route::get(
    'admin-payments-pdf',
    [AdminPaymentController::class, 'exportAdminPaymentsPDF']
)->name('admin.payments.pdf');
Route::get(
    'transactions-excel',
    [PaymentController::class, 'exportTransactionsExcel']
)->name('admin.transactionsExcel');
// export transactions pdf admin route
Route::get(
    'transactions-pdf',
    [PaymentController::class, 'exportTransactionsPdf']
)->name('admin.export.transactions.pdf');

Route::prefix('client')->group(function () {

    //Payments
    Route::post('payments', [Client\PaymentController::class, 'store'])->name('clients.payments.store');
    Route::post('stripe-payment', [Client\StripeController::class, 'createSession'])->name('client.stripe-payment');
    Route::get('razorpayonboard', [Client\RazorpayController::class, 'onBoard'])->name('razorpay.init');
    Route::get('paypal-onboard', [Client\PaypalController::class, 'onBoard'])->name('paypal.init');

    Route::get('payment-success', [Client\StripeController::class, 'paymentSuccess'])->name('payment-success');
    Route::get('failed-payment', [Client\StripeController::class, 'handleFailedPayment'])->name('failed-payment');

    Route::get('paypal-payment-success', [Client\PaypalController::class, 'success'])->name('paypal.success');
    Route::get('paypal-payment-failed', [Client\PaypalController::class, 'failed'])->name('paypal.failed');

    Route::get(
        'invoices/{invoice}',
        [Client\InvoiceController::class, 'show']
    )->name('invoices.show');

    // razorpay payment
    Route::post('razorpay-payment-success', [Client\RazorpayController::class, 'paymentSuccess'])
        ->name('razorpay.success');
    Route::get('razorpay-payment-failed', [Client\RazorpayController::class, 'paymentFailed'])
        ->name('razorpay.failed');
    Route::get('razorpay-payment-webhook', [Client\RazorpayController::class, 'paymentSuccessWebHook'])
        ->name('razorpay.webhook');

    // Paystack Payment Route
    Route::get('/paystack-onboard', [Client\PaystackController::class, 'redirectToGateway'])->name('client.paystack.init');
    Route::any(
        '/paystack-payment-success',
        [Client\PaystackController::class, 'handleGatewayCallback']
    )->name('client.paystack.success');

    Route::get('mercadopago-success', [Client\MercadopagoController::class, 'success'])->name('mercadopago.success')->withoutMiddleware('auth');
});




Route::get('/upgrade/database', function () {
    if (config('app.enable_upgrade_route')) {
        Artisan::call('migrate', [
            '--force' => true,
        ]);

        return redirect(route('filament.admin.auth.login'));
    }

    return redirect(route('filament.admin.auth.login'));
});

Route::get(
    'invoices/{invoice}',
    [Client\InvoiceController::class, 'show']
)->name('client.invoices.show');

// 🔥 BEAST MODE: Test route for web migration (outside middleware)
Route::get('/test-web-migration', function() {
    try {
        return response()->json([
            'success' => true,
            'message' => 'Web migration controller test route working',
            'timestamp' => now(),
            'php_version' => phpversion()
        ]);
    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
});

// 🔥 BEAST MODE: Direct test for WebMigrationController (outside middleware)
Route::get('/test-web-migration-controller', [\App\Http\Controllers\Install\WebMigrationController::class, 'test']);

// 🔥 BEAST MODE: Additional test routes for debugging
Route::get('/test-web-migration-status', [\App\Http\Controllers\Install\WebMigrationController::class, 'getStatus']);
Route::post('/test-web-migration-run', [\App\Http\Controllers\Install\WebMigrationController::class, 'runMigrations']);

// 🔥 BEAST MODE: Diagnostics page
Route::get('/install-diagnostics', function() {
    return view('install.diagnostics');
});

// 🔥 BEAST MODE: Post-installation fixes
Route::get('/post-installation-fixes', function() {
    return view('install.post-installation-fixes');
});

// 🔥 BEAST MODE: Database diagnostics
Route::get('/database-diagnostics', function() {
    return view('install.database-diagnostics');
});

// 🔥 BEAST MODE: Database connection testing
Route::post('/test-database-connection', function(\Illuminate\Http\Request $request) {
    try {
        $config = $request->validate([
            'host' => 'required|string',
            'port' => 'required|string',
            'database' => 'required|string',
            'username' => 'required|string',
            'password' => 'nullable|string'
        ]);

        // Test database connection with provided credentials
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']}";

        try {
            $pdo = new PDO($dsn, $config['username'], $config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 5
            ]);

            // Test a simple query
            $stmt = $pdo->query('SELECT 1');
            $result = $stmt->fetch();

            return response()->json([
                'success' => true,
                'message' => 'Database connection successful! Connected to ' . $config['database'],
                'server_info' => $pdo->getAttribute(PDO::ATTR_SERVER_VERSION)
            ]);

        } catch (PDOException $e) {
            return response()->json([
                'success' => false,
                'error' => 'Database connection failed: ' . $e->getMessage(),
                'error_code' => $e->getCode()
            ]);
        }

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => 'Validation failed: ' . $e->getMessage()
        ]);
    }
});

// 🔥 BEAST MODE: Test current database configuration
Route::get('/test-current-database', function() {
    try {
        $pdo = DB::connection()->getPdo();
        $dbName = DB::connection()->getDatabaseName();

        return response()->json([
            'success' => true,
            'message' => 'Current database configuration is working!',
            'database' => $dbName,
            'driver' => config('database.default'),
            'host' => config('database.connections.mysql.host'),
            'port' => config('database.connections.mysql.port')
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'config' => [
                'driver' => config('database.default'),
                'host' => config('database.connections.mysql.host'),
                'port' => config('database.connections.mysql.port'),
                'database' => config('database.connections.mysql.database'),
                'username' => config('database.connections.mysql.username')
            ]
        ]);
    }
});

// 🔥 BEAST MODE: Auth diagnostics
Route::get('/auth-diagnostics', function() {
    try {
        $users = \App\Models\User::with('roles')->get();
        $roles = \App\Models\Role::all();

        $diagnostics = [
            'users_count' => $users->count(),
            'roles_count' => $roles->count(),
            'users' => $users->map(function($user) {
                return [
                    'id' => $user->id,
                    'email' => $user->email,
                    'name' => $user->first_name . ' ' . $user->last_name,
                    'is_default_admin' => $user->is_default_admin ?? false,
                    'roles' => $user->roles->pluck('name')->toArray(),
                    'roles_count' => $user->roles->count()
                ];
            }),
            'roles' => $roles->map(function($role) {
                return [
                    'id' => $role->id,
                    'name' => $role->name,
                    'guard_name' => $role->guard_name,
                    'users_count' => $role->users()->count()
                ];
            })
        ];

        return response()->json($diagnostics, 200, [], JSON_PRETTY_PRINT);

    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

// 🔥 BEAST MODE: Fix user table for Filament compatibility
Route::get('/fix-user-table', function() {
    try {
        $results = [];

        // Check if name column exists
        if (!\Illuminate\Support\Facades\Schema::hasColumn('users', 'name')) {
            \Illuminate\Support\Facades\Schema::table('users', function ($table) {
                $table->string('name')->nullable()->after('id');
            });
            $results[] = "✅ Added name column to users table";
        } else {
            $results[] = "ℹ️  Name column already exists";
        }

        // Update existing users
        $users = \App\Models\User::whereNull('name')->orWhere('name', '')->get();
        $updated = 0;

        foreach ($users as $user) {
            $name = trim($user->first_name . ' ' . $user->last_name);
            if ($name) {
                $user->update(['name' => $name]);
                $updated++;
            }
        }

        $results[] = "✅ Updated {$updated} users with name field";

        return response()->json([
            'success' => true,
            'results' => $results
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
});

Route::group(['prefix' => 'install', 'as' => 'install.', 'middleware' => ['web', 'installed', 'installation.session', 'installation.csrf']], function () {
    Route::get('/', [\App\Http\Controllers\Install\InstallController::class, 'index'])->name('index');
    Route::get('/server-requirements', [\App\Http\Controllers\Install\InstallController::class, 'serverRequirements'])->name('server-requirements');
    Route::get('/folder-permissions', [\App\Http\Controllers\Install\InstallController::class, 'folderPermissions'])->name('folder-permissions');
    Route::get('/database', [\App\Http\Controllers\Install\InstallController::class, 'database'])->name('database');
    Route::post('/database', [\App\Http\Controllers\Install\InstallController::class, 'saveDatabase']);
    Route::post('/test-database', [\App\Http\Controllers\Install\InstallController::class, 'testDatabase'])->name('test-database');
    Route::get('/mail', [\App\Http\Controllers\Install\InstallController::class, 'mail'])->name('mail');
    Route::post('/mail', [\App\Http\Controllers\Install\InstallController::class, 'saveMail']);
    Route::post('/test-mail', [\App\Http\Controllers\Install\InstallController::class, 'testMail'])->name('test-mail');
    Route::get('/admin', [\App\Http\Controllers\Install\InstallController::class, 'admin'])->name('admin');
    Route::post('/admin', [\App\Http\Controllers\Install\InstallController::class, 'saveAdmin']);
    Route::get('/branding', [\App\Http\Controllers\Install\InstallController::class, 'branding'])->name('branding');
    Route::post('/branding', [\App\Http\Controllers\Install\InstallController::class, 'saveBranding']);
    Route::get('/summary', [\App\Http\Controllers\Install\InstallController::class, 'summary'])->name('summary');
    Route::get('/finalize', [\App\Http\Controllers\Install\InstallController::class, 'finalize'])->name('finalize');
    Route::get('/success', [\App\Http\Controllers\Install\InstallController::class, 'success'])->name('success');
    Route::get('/rollback', [\App\Http\Controllers\Install\InstallController::class, 'rollback'])->name('rollback');
    Route::get('/status', [\App\Http\Controllers\Install\InstallController::class, 'status'])->name('status');

    // 🔥 BEAST MODE: Web-based migration tool for shared hosting
    Route::get('/web-migration', [\App\Http\Controllers\Install\WebMigrationController::class, 'index'])->name('web-migration');
});

// 🔥 BEAST MODE: Web migration API routes (without 'installed' middleware to prevent redirects)
Route::group(['prefix' => 'install', 'as' => 'install.', 'middleware' => ['web', 'installation.session']], function () {
    Route::get('/web-migration/test', [\App\Http\Controllers\Install\WebMigrationController::class, 'test'])->name('web-migration.test');
    Route::get('/web-migration/status', [\App\Http\Controllers\Install\WebMigrationController::class, 'getStatus'])->name('web-migration.status');
    Route::post('/web-migration/migrations', [\App\Http\Controllers\Install\WebMigrationController::class, 'runMigrations'])->name('web-migration.migrations');
    Route::post('/web-migration/seeders', [\App\Http\Controllers\Install\WebMigrationController::class, 'runSeeders'])->name('web-migration.seeders');
});
