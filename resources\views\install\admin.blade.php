@extends('install.layouts.master')

@section('title', 'Admin Account')

@section('content')
<div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-shield text-2xl text-white"></i>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-white">Create Administrator Account</h1>
                        <p class="text-blue-100 mt-1">Set up your admin credentials to manage the system</p>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="px-8 py-6">
                <form action="{{ route('install.admin') }}" method="POST" class="space-y-6" x-data="adminForm()">
                    @csrf

                    <!-- Admin Details -->
                    <div class="space-y-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700">Full Name</label>
                            <input type="text"
                                   id="name"
                                   name="name"
                                   value="{{ old('name') }}"
                                   required
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="Enter your full name">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                            <input type="email"
                                   id="email"
                                   name="email"
                                   value="{{ old('email') }}"
                                   required
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="<EMAIL>">
                            @error('email')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                            <div class="relative">
                                <input type="password"
                                       id="password"
                                       name="password"
                                       required
                                       x-model="password"
                                       @input="checkPasswordStrength"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm pr-10"
                                       placeholder="Enter a strong password">
                                <button type="button"
                                        @click="showPassword = !showPassword"
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <i class="fas" :class="showPassword ? 'fa-eye-slash' : 'fa-eye'" class="text-gray-400"></i>
                                </button>
                            </div>

                            <!-- Password Strength Indicator -->
                            <div class="mt-2">
                                <div class="flex items-center space-x-2">
                                    <div class="flex-1 bg-gray-200 rounded-full h-2">
                                        <div class="h-2 rounded-full transition-all duration-300"
                                             :class="passwordStrengthColor"
                                             :style="`width: ${passwordStrength}%`"></div>
                                    </div>
                                    <span class="text-sm font-medium" :class="passwordStrengthTextColor" x-text="passwordStrengthText"></span>
                                </div>
                                <div class="mt-1 text-xs text-gray-600">
                                    Password should be at least 8 characters with uppercase, lowercase, numbers, and symbols
                                </div>
                            </div>

                            @error('password')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                            <input type="password"
                                   id="password_confirmation"
                                   name="password_confirmation"
                                   required
                                   x-model="passwordConfirmation"
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                   placeholder="Confirm your password">

                            <div x-show="passwordConfirmation && password !== passwordConfirmation" class="mt-1 text-sm text-red-600">
                                Passwords do not match
                            </div>

                            @error('password_confirmation')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Security Notice -->
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex">
                            <i class="fas fa-shield-alt text-yellow-500 mr-3 mt-0.5"></i>
                            <div>
                                <h3 class="font-medium text-yellow-800">Security Notice</h3>
                                <div class="text-sm text-yellow-700 mt-1">
                                    <ul class="list-disc list-inside space-y-1">
                                        <li>This account will have full administrative access to the system</li>
                                        <li>Use a strong, unique password that you don't use elsewhere</li>
                                        <li>Keep these credentials secure and don't share them</li>
                                        <li>You can create additional admin accounts later from the admin panel</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 🔥 BEAST MODE Error Messages -->
                    @if($errors->has('admin_creation'))
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                            <div class="flex items-start">
                                <i class="fas fa-exclamation-triangle text-red-500 mr-3 mt-0.5"></i>
                                <div class="flex-1">
                                    <div class="text-red-800 font-medium mb-2">🚨 Admin Account Creation Failed</div>
                                    <div class="text-red-700 text-sm mb-3">{{ $errors->first('admin_creation') }}</div>

                                    @if($errors->has('recovery_instructions') && !empty($errors->get('recovery_instructions')[0]))
                                        <div class="mt-4">
                                            <h4 class="font-medium text-red-800 mb-2">🔧 Recovery Instructions:</h4>
                                            <div class="bg-gray-800 text-green-400 p-3 rounded text-sm font-mono">
                                                @foreach($errors->get('recovery_instructions')[0] as $instruction)
                                                    <div class="mb-1">{{ $instruction }}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif

                                    <div class="mt-4 p-3 bg-blue-100 rounded">
                                        <h4 class="font-medium text-blue-800 mb-2">🚑 Emergency Fix:</h4>
                                        <p class="text-blue-700 text-sm mb-2">
                                            If the error persists, run the emergency fix script:
                                        </p>
                                        <div class="bg-gray-800 text-green-400 p-2 rounded text-xs font-mono">
                                            php fix-database-migration-error.php
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @elseif($errors->any())
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex">
                                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
                                <div>
                                    <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                                    <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Footer Buttons -->
                    <div class="flex items-center justify-between pt-6 border-t">
                        <a href="{{ route('install.mail') }}"
                           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back
                        </a>

                        <button type="submit"
                                :disabled="!isFormValid"
                                class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                            Create Account & Continue
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function adminForm() {
    return {
        password: '',
        passwordConfirmation: '',
        showPassword: false,
        passwordStrength: 0,
        passwordStrengthText: '',
        passwordStrengthColor: 'bg-gray-300',
        passwordStrengthTextColor: 'text-gray-500',

        get isFormValid() {
            return this.password.length >= 8 &&
                   this.password === this.passwordConfirmation &&
                   this.passwordStrength >= 60;
        },

        checkPasswordStrength() {
            let strength = 0;
            let feedback = [];

            // Length check
            if (this.password.length >= 8) strength += 20;
            else feedback.push('at least 8 characters');

            // Uppercase check
            if (/[A-Z]/.test(this.password)) strength += 20;
            else feedback.push('uppercase letter');

            // Lowercase check
            if (/[a-z]/.test(this.password)) strength += 20;
            else feedback.push('lowercase letter');

            // Number check
            if (/\d/.test(this.password)) strength += 20;
            else feedback.push('number');

            // Special character check
            if (/[^A-Za-z0-9]/.test(this.password)) strength += 20;
            else feedback.push('special character');

            this.passwordStrength = strength;

            if (strength < 40) {
                this.passwordStrengthText = 'Weak';
                this.passwordStrengthColor = 'bg-red-500';
                this.passwordStrengthTextColor = 'text-red-600';
            } else if (strength < 80) {
                this.passwordStrengthText = 'Medium';
                this.passwordStrengthColor = 'bg-yellow-500';
                this.passwordStrengthTextColor = 'text-yellow-600';
            } else {
                this.passwordStrengthText = 'Strong';
                this.passwordStrengthColor = 'bg-green-500';
                this.passwordStrengthTextColor = 'text-green-600';
            }
        }
    }
}
</script>
@endpush
@endsection