<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Client;
use App\Models\Currency;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

class ReportingService
{
    /**
     * Get comprehensive revenue analytics for a date range
     */
    public function getRevenueAnalytics(Carbon $startDate, Carbon $endDate, ?int $currencyId = null): array
    {
        $query = Payment::whereIsApproved(Payment::APPROVED)
            ->whereBetween('payment_date', [$startDate, $endDate]);

        if ($currencyId) {
            $query->whereHas('invoice', function ($q) use ($currencyId) {
                $q->where('currency_id', $currencyId);
            });
        }

        $payments = $query->with(['invoice.client.user', 'invoice.currency'])->get();

        return [
            'total_revenue' => $payments->sum('amount'),
            'payment_count' => $payments->count(),
            'average_payment' => $payments->count() > 0 ? $payments->sum('amount') / $payments->count() : 0,
            'revenue_by_currency' => $this->groupRevenueByCurrency($payments),
            'revenue_by_client' => $this->groupRevenueByClient($payments),
            'daily_revenue' => $this->getDailyRevenue($payments, $startDate, $endDate),
        ];
    }

    /**
     * Get outstanding invoice analytics
     */
    public function getOutstandingAnalytics(?int $currencyId = null): array
    {
        $query = Invoice::whereIn('status', [Invoice::UNPAID, Invoice::PARTIALLY, Invoice::OVERDUE]);

        if ($currencyId) {
            $query->where('currency_id', $currencyId);
        }

        $invoices = $query->with(['client.user', 'currency', 'payments'])->get();

        $totalOutstanding = 0;
        $overdueAmount = 0;
        $agingData = ['0-30' => 0, '31-60' => 0, '61-90' => 0, '90+' => 0];

        foreach ($invoices as $invoice) {
            $outstanding = $invoice->final_amount - $invoice->payments->sum('amount');
            $totalOutstanding += $outstanding;

            if ($invoice->status === Invoice::OVERDUE) {
                $overdueAmount += $outstanding;
            }

            // Calculate aging
            $daysPastDue = now()->diffInDays($invoice->due_date, false);
            if ($daysPastDue <= 30) {
                $agingData['0-30'] += $outstanding;
            } elseif ($daysPastDue <= 60) {
                $agingData['31-60'] += $outstanding;
            } elseif ($daysPastDue <= 90) {
                $agingData['61-90'] += $outstanding;
            } else {
                $agingData['90+'] += $outstanding;
            }
        }

        return [
            'total_outstanding' => $totalOutstanding,
            'overdue_amount' => $overdueAmount,
            'invoice_count' => $invoices->count(),
            'aging_analysis' => $agingData,
            'outstanding_by_client' => $this->groupOutstandingByClient($invoices),
        ];
    }

    /**
     * Get payment collection rate analytics
     */
    public function getCollectionRateAnalytics(Carbon $startDate, Carbon $endDate): array
    {
        $invoices = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', Invoice::DRAFT)
            ->with(['payments'])
            ->get();

        $totalInvoiced = $invoices->sum('final_amount');
        $totalCollected = $invoices->sum(function ($invoice) {
            return $invoice->payments->sum('amount');
        });

        $collectionRate = $totalInvoiced > 0 ? ($totalCollected / $totalInvoiced) * 100 : 0;

        // Calculate average payment time
        $paidInvoices = $invoices->filter(function ($invoice) {
            return $invoice->status === Invoice::PAID;
        });

        $averagePaymentTime = 0;
        if ($paidInvoices->count() > 0) {
            $totalDays = $paidInvoices->sum(function ($invoice) {
                $lastPayment = $invoice->payments->sortByDesc('payment_date')->first();
                return $lastPayment ? Carbon::parse($invoice->invoice_date)->diffInDays($lastPayment->payment_date) : 0;
            });
            $averagePaymentTime = $totalDays / $paidInvoices->count();
        }

        return [
            'total_invoiced' => $totalInvoiced,
            'total_collected' => $totalCollected,
            'collection_rate' => round($collectionRate, 2),
            'average_payment_time' => round($averagePaymentTime, 1),
            'paid_invoices_count' => $paidInvoices->count(),
            'total_invoices_count' => $invoices->count(),
        ];
    }

    /**
     * Get client payment behavior analysis
     */
    public function getClientBehaviorAnalysis(): array
    {
        $clients = Client::with(['invoices.payments', 'user'])->get();

        $clientAnalysis = $clients->map(function ($client) {
            $invoices = $client->invoices->where('status', '!=', Invoice::DRAFT);
            $totalInvoiced = $invoices->sum('final_amount');
            $totalPaid = $invoices->sum(function ($invoice) {
                return $invoice->payments->sum('amount');
            });

            $paidInvoices = $invoices->where('status', Invoice::PAID);
            $overdueInvoices = $invoices->where('status', Invoice::OVERDUE);

            $averagePaymentTime = 0;
            if ($paidInvoices->count() > 0) {
                $totalDays = $paidInvoices->sum(function ($invoice) {
                    $lastPayment = $invoice->payments->sortByDesc('payment_date')->first();
                    return $lastPayment ? Carbon::parse($invoice->invoice_date)->diffInDays($lastPayment->payment_date) : 0;
                });
                $averagePaymentTime = $totalDays / $paidInvoices->count();
            }

            // Calculate overdue amount
            $overdueAmount = $overdueInvoices->sum(function ($invoice) {
                return $invoice->final_amount - $invoice->payments->sum('amount');
            });

            // Calculate risk score (0-100, higher = more risky)
            $riskScore = 0;
            if ($invoices->count() > 0) {
                $overdueRatio = $overdueInvoices->count() / $invoices->count();
                $paymentRatio = $totalInvoiced > 0 ? $totalPaid / $totalInvoiced : 0;
                $timeRisk = $averagePaymentTime > 30 ? min(($averagePaymentTime - 30) / 30, 1) : 0;

                $riskScore = round(
                    ($overdueRatio * 40) + // 40% weight for overdue ratio
                    ((1 - $paymentRatio) * 40) + // 40% weight for unpaid ratio
                    ($timeRisk * 20), // 20% weight for payment time
                    0
                );
            }

            return [
                'client_id' => $client->id,
                'client_name' => $client->user->full_name,
                'total_invoiced' => round($totalInvoiced, 2),
                'total_paid' => round($totalPaid, 2),
                'payment_rate' => $totalInvoiced > 0 ? round(($totalPaid / $totalInvoiced) * 100, 1) : 0,
                'invoice_count' => $invoices->count(),
                'paid_count' => $paidInvoices->count(),
                'overdue_count' => $overdueInvoices->count(),
                'average_payment_time' => round($averagePaymentTime, 1),
                'overdue_amount' => round($overdueAmount, 2),
                'risk_score' => $riskScore,
            ];
        })->sortByDesc('total_invoiced');

        return [
            'client_analysis' => $clientAnalysis->values()->toArray(),
            'total_clients' => $clientAnalysis->count(),
            'high_risk_clients' => $clientAnalysis->where('risk_score', '>=', 70)->count(),
            'average_payment_time' => $clientAnalysis->avg('average_payment_time'),
            'top_client_revenue' => $clientAnalysis->first()['total_paid'] ?? 0,
        ];
    }

    /**
     * Get revenue by product/service category
     */
    public function getRevenueByCategory(Carbon $startDate, Carbon $endDate): array
    {
        $payments = Payment::whereIsApproved(Payment::APPROVED)
            ->whereBetween('payment_date', [$startDate, $endDate])
            ->with(['invoice.invoiceItems.product.category', 'invoice.invoiceItems.service.category'])
            ->get();

        $categoryRevenue = [];

        foreach ($payments as $payment) {
            $invoice = $payment->invoice;
            $paymentRatio = $invoice->final_amount > 0 ? $payment->amount / $invoice->final_amount : 0;

            foreach ($invoice->invoiceItems as $item) {
                $categoryName = 'Uncategorized';
                
                if ($item->product && $item->product->category) {
                    $categoryName = $item->product->category->name;
                } elseif ($item->service && $item->service->category) {
                    $categoryName = $item->service->category->name;
                }

                $itemTotal = $item->quantity * $item->unit_price;
                $allocatedRevenue = $itemTotal * $paymentRatio;

                if (!isset($categoryRevenue[$categoryName])) {
                    $categoryRevenue[$categoryName] = 0;
                }
                $categoryRevenue[$categoryName] += $allocatedRevenue;
            }
        }

        arsort($categoryRevenue);
        return $categoryRevenue;
    }

    /**
     * Get cash flow projections
     */
    public function getCashFlowProjections(int $months = 6): array
    {
        $monthlyProjections = [];
        $startDate = now()->startOfMonth();
        $cumulativeCashFlow = 0;

        for ($i = 0; $i < $months; $i++) {
            $monthStart = $startDate->copy()->addMonths($i);
            $monthEnd = $monthStart->copy()->endOfMonth();

            // Expected income from invoices due this month
            $expectedIncome = Invoice::whereIn('status', [Invoice::UNPAID, Invoice::PARTIALLY])
                ->whereBetween('due_date', [$monthStart, $monthEnd])
                ->sum(DB::raw('final_amount - (SELECT COALESCE(SUM(amount), 0) FROM payments WHERE invoice_id = invoices.id)'));

            // Estimate expenses (simplified - could be enhanced with actual expense tracking)
            $expectedExpenses = $expectedIncome * 0.3; // Assume 30% expense ratio

            $netCashFlow = $expectedIncome - $expectedExpenses;
            $cumulativeCashFlow += $netCashFlow;

            $monthlyProjections[] = [
                'month' => $monthStart->format('M Y'),
                'expected_income' => round($expectedIncome, 2),
                'expected_expenses' => round($expectedExpenses, 2),
                'net_cash_flow' => round($netCashFlow, 2),
                'cumulative_cash_flow' => round($cumulativeCashFlow, 2),
            ];
        }

        return [
            'monthly_projections' => $monthlyProjections,
            'total_projected_income' => round(collect($monthlyProjections)->sum('expected_income'), 2),
            'total_projected_expenses' => round(collect($monthlyProjections)->sum('expected_expenses'), 2),
            'final_balance' => round($cumulativeCashFlow, 2),
        ];
    }

    /**
     * Helper method to group revenue by currency
     */
    private function groupRevenueByCurrency(Collection $payments): array
    {
        return $payments->groupBy('invoice.currency_id')->map(function ($currencyPayments) {
            return [
                'currency' => $currencyPayments->first()->invoice->currency->name ?? 'Default',
                'total' => $currencyPayments->sum('amount'),
                'count' => $currencyPayments->count(),
            ];
        })->values()->toArray();
    }

    /**
     * Helper method to group revenue by client
     */
    private function groupRevenueByClient(Collection $payments): array
    {
        return $payments->groupBy('invoice.client_id')->map(function ($clientPayments) {
            $client = $clientPayments->first()->invoice->client;
            return [
                'client_id' => $client->id,
                'client_name' => $client->user->full_name,
                'total' => $clientPayments->sum('amount'),
                'count' => $clientPayments->count(),
            ];
        })->sortByDesc('total')->values()->toArray();
    }

    /**
     * Helper method to group outstanding by client
     */
    private function groupOutstandingByClient(Collection $invoices): array
    {
        return $invoices->groupBy('client_id')->map(function ($clientInvoices) {
            $client = $clientInvoices->first()->client;
            $totalOutstanding = $clientInvoices->sum(function ($invoice) {
                return $invoice->final_amount - $invoice->payments->sum('amount');
            });

            return [
                'client_id' => $client->id,
                'client_name' => $client->user->full_name,
                'total_outstanding' => $totalOutstanding,
                'invoice_count' => $clientInvoices->count(),
            ];
        })->sortByDesc('total_outstanding')->values()->toArray();
    }

    /**
     * Helper method to get daily revenue breakdown
     */
    private function getDailyRevenue(Collection $payments, Carbon $startDate, Carbon $endDate): array
    {
        $dailyRevenue = $payments->groupBy(function ($payment) {
            return Carbon::parse($payment->payment_date)->format('Y-m-d');
        })->map(function ($dayPayments) {
            return $dayPayments->sum('amount');
        });

        $period = CarbonPeriod::create($startDate, $endDate);
        $result = [];

        foreach ($period as $date) {
            $dateKey = $date->format('Y-m-d');
            $result[] = [
                'date' => $dateKey,
                'revenue' => $dailyRevenue->get($dateKey, 0),
            ];
        }

        return $result;
    }
}
