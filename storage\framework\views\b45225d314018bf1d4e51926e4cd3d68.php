<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<script>
    // Set CSRF token for AJAX requests
    window.Laravel = {
        csrfToken: '<?php echo e(csrf_token()); ?>'
    };

    // Configure Livewire before it loads
    document.addEventListener('DOMContentLoaded', function() {
        if (window.Livewire) {
            // Livewire is already loaded
            console.log('Livewire loaded successfully');
        } else {
            // Wait for Livewire to load
            document.addEventListener('livewire:init', function() {
                console.log('Livewire initialized');
            });
        }
    });
</script>
<?php /**PATH C:\xampp\htdocs\invoiceswi\resources\views/filament/csrf-token.blade.php ENDPATH**/ ?>