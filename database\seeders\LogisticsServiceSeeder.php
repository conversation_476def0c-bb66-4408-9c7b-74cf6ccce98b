<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Service;
use App\Models\Category;

class LogisticsServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create logistics service categories
        $categories = [
            'Delivery Services' => 'Standard delivery and courier services',
            'Freight Transport' => 'Heavy freight and cargo transportation',
            'Warehousing' => 'Storage and warehouse management services',
            'Express Services' => 'Time-sensitive and urgent delivery services',
            'International' => 'Cross-border and international shipping',
            'Specialized' => 'Specialized logistics services',
        ];

        $categoryModels = [];
        foreach ($categories as $name => $description) {
            $categoryModels[$name] = Category::firstOrCreate([
                'name' => $name,
            ], [
                'description' => $description,
            ]);
        }

        // Create logistics services
        $services = [
            // Delivery Services
            [
                'name' => 'Standard Delivery',
                'code' => 'STD-DEL',
                'category' => 'Delivery Services',
                'unit_price' => 15.00,
                'unit_type' => 'per_package',
                'description' => 'Standard delivery service within city limits, 3-5 business days',
                'pricing_tiers' => [
                    ['min_quantity' => 1, 'max_quantity' => 5, 'price' => 15.00],
                    ['min_quantity' => 6, 'max_quantity' => 20, 'price' => 12.00],
                    ['min_quantity' => 21, 'max_quantity' => null, 'price' => 10.00],
                ],
            ],
            [
                'name' => 'Same Day Delivery',
                'code' => 'SAME-DAY',
                'category' => 'Express Services',
                'unit_price' => 35.00,
                'unit_type' => 'per_package',
                'description' => 'Same day delivery within metropolitan area',
            ],
            [
                'name' => 'Next Day Delivery',
                'code' => 'NEXT-DAY',
                'category' => 'Express Services',
                'unit_price' => 25.00,
                'unit_type' => 'per_package',
                'description' => 'Guaranteed next business day delivery',
            ],

            // Freight Transport
            [
                'name' => 'Local Freight',
                'code' => 'LOC-FRT',
                'category' => 'Freight Transport',
                'unit_price' => 2.50,
                'unit_type' => 'per_kg',
                'description' => 'Local freight transport for heavy items',
                'pricing_tiers' => [
                    ['min_quantity' => 1, 'max_quantity' => 100, 'price' => 2.50],
                    ['min_quantity' => 101, 'max_quantity' => 500, 'price' => 2.00],
                    ['min_quantity' => 501, 'max_quantity' => null, 'price' => 1.50],
                ],
            ],
            [
                'name' => 'Long Distance Freight',
                'code' => 'LD-FRT',
                'category' => 'Freight Transport',
                'unit_price' => 0.85,
                'unit_type' => 'per_km',
                'description' => 'Long distance freight transport, per kilometer pricing',
            ],
            [
                'name' => 'Full Truck Load',
                'code' => 'FTL',
                'category' => 'Freight Transport',
                'unit_price' => 1200.00,
                'unit_type' => 'flat_rate',
                'description' => 'Full truck load service for large shipments',
            ],

            // Warehousing
            [
                'name' => 'Storage Service',
                'code' => 'STORAGE',
                'category' => 'Warehousing',
                'unit_price' => 5.00,
                'unit_type' => 'per_cubic_meter',
                'description' => 'Monthly storage service in secure warehouse',
            ],
            [
                'name' => 'Pick & Pack',
                'code' => 'PICK-PACK',
                'category' => 'Warehousing',
                'unit_price' => 3.50,
                'unit_type' => 'per_package',
                'description' => 'Order fulfillment and packaging service',
            ],
            [
                'name' => 'Inventory Management',
                'code' => 'INV-MGMT',
                'category' => 'Warehousing',
                'unit_price' => 150.00,
                'unit_type' => 'per_day',
                'description' => 'Professional inventory management service',
            ],

            // International
            [
                'name' => 'International Express',
                'code' => 'INTL-EXP',
                'category' => 'International',
                'unit_price' => 45.00,
                'unit_type' => 'per_package',
                'description' => 'Express international shipping, 2-3 business days',
            ],
            [
                'name' => 'International Standard',
                'code' => 'INTL-STD',
                'category' => 'International',
                'unit_price' => 25.00,
                'unit_type' => 'per_package',
                'description' => 'Standard international shipping, 7-14 business days',
            ],
            [
                'name' => 'Customs Clearance',
                'code' => 'CUSTOMS',
                'category' => 'International',
                'unit_price' => 75.00,
                'unit_type' => 'flat_rate',
                'description' => 'Customs clearance and documentation service',
            ],

            // Specialized
            [
                'name' => 'White Glove Delivery',
                'code' => 'WG-DEL',
                'category' => 'Specialized',
                'unit_price' => 85.00,
                'unit_type' => 'per_package',
                'description' => 'Premium delivery with unpacking and setup',
            ],
            [
                'name' => 'Temperature Controlled',
                'code' => 'TEMP-CTRL',
                'category' => 'Specialized',
                'unit_price' => 4.00,
                'unit_type' => 'per_kg',
                'description' => 'Temperature controlled transport for sensitive items',
            ],
            [
                'name' => 'Hazmat Transport',
                'code' => 'HAZMAT',
                'category' => 'Specialized',
                'unit_price' => 125.00,
                'unit_type' => 'flat_rate',
                'description' => 'Specialized transport for hazardous materials',
            ],
            [
                'name' => 'Installation Service',
                'code' => 'INSTALL',
                'category' => 'Specialized',
                'unit_price' => 65.00,
                'unit_type' => 'per_hour',
                'description' => 'Professional installation and setup service',
            ],
        ];

        foreach ($services as $serviceData) {
            $category = $categoryModels[$serviceData['category']];
            
            Service::firstOrCreate([
                'code' => $serviceData['code'],
            ], [
                'name' => $serviceData['name'],
                'category_id' => $category->id,
                'unit_price' => $serviceData['unit_price'],
                'unit_type' => $serviceData['unit_type'],
                'description' => $serviceData['description'],
                'is_active' => true,
                'pricing_tiers' => $serviceData['pricing_tiers'] ?? null,
            ]);
        }

        $this->command->info('Logistics services seeded successfully!');
    }
}
