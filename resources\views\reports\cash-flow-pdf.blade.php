<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Cash Flow Projections Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #9333ea;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .summary-section {
            margin-bottom: 25px;
        }
        .summary-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .summary-item {
            display: table-cell;
            width: 25%;
            padding: 10px;
            border: 1px solid #ddd;
            text-align: center;
            vertical-align: top;
        }
        .summary-item h3 {
            margin: 0 0 5px 0;
            font-size: 14px;
            color: #666;
        }
        .summary-item .value {
            font-size: 18px;
            font-weight: bold;
            color: #9333ea;
        }
        .value.positive {
            color: #059669;
        }
        .value.negative {
            color: #dc2626;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0 10px 0;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .positive {
            color: #059669;
            font-weight: bold;
        }
        .negative {
            color: #dc2626;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .alert {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            color: #991b1b;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .highlight {
            background-color: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Cash Flow Projections Report</h1>
        <p>Generated on: {{ now()->format('F j, Y \a\t g:i A') }}</p>
        @if(isset($filters['months']))
        <p>Projection Period: {{ $filters['months'] }} months</p>
        @endif
    </div>

    <!-- Cash Flow Alert -->
    @php
        $finalBalance = $data['final_balance'] ?? 0;
        $hasNegativeMonths = collect($data['monthly_projections'] ?? [])->where('net_cash_flow', '<', 0)->count() > 0;
    @endphp
    
    @if($finalBalance < 0)
    <div class="alert">
        <strong>Cash Flow Warning:</strong> Projected final balance is negative ({{ getCurrencyAmount($finalBalance, true) }}). Consider adjusting expenses or accelerating collections.
    </div>
    @elseif($finalBalance > 0)
    <div class="highlight">
        <strong>Positive Outlook:</strong> Projected final balance is positive ({{ getCurrencyAmount($finalBalance, true) }}). Strong cash flow position expected.
    </div>
    @endif

    <!-- Summary Section -->
    <div class="summary-section">
        <div class="section-title">Cash Flow Summary</div>
        <div class="summary-grid">
            <div class="summary-item">
                <h3>Total Projected Income</h3>
                <div class="value positive">{{ getCurrencyAmount($data['total_projected_income'] ?? 0, true) }}</div>
            </div>
            <div class="summary-item">
                <h3>Total Projected Expenses</h3>
                <div class="value negative">{{ getCurrencyAmount($data['total_projected_expenses'] ?? 0, true) }}</div>
            </div>
            <div class="summary-item">
                <h3>Net Cash Flow</h3>
                <div class="value {{ ($data['total_projected_income'] ?? 0) - ($data['total_projected_expenses'] ?? 0) >= 0 ? 'positive' : 'negative' }}">
                    {{ getCurrencyAmount(($data['total_projected_income'] ?? 0) - ($data['total_projected_expenses'] ?? 0), true) }}
                </div>
            </div>
            <div class="summary-item">
                <h3>Final Balance</h3>
                <div class="value {{ $finalBalance >= 0 ? 'positive' : 'negative' }}">{{ getCurrencyAmount($finalBalance, true) }}</div>
            </div>
        </div>
    </div>

    <!-- Monthly Projections -->
    @if(isset($data['monthly_projections']) && !empty($data['monthly_projections']))
    <div class="section-title">Monthly Cash Flow Projections</div>
    <table>
        <thead>
            <tr>
                <th>Month</th>
                <th class="text-right">Expected Income</th>
                <th class="text-right">Expected Expenses</th>
                <th class="text-right">Net Cash Flow</th>
                <th class="text-right">Cumulative Balance</th>
                <th class="text-center">Status</th>
            </tr>
        </thead>
        <tbody>
            @foreach($data['monthly_projections'] as $projection)
            <tr>
                <td>{{ $projection['month'] }}</td>
                <td class="text-right">{{ getCurrencyAmount($projection['expected_income'], true) }}</td>
                <td class="text-right">{{ getCurrencyAmount($projection['expected_expenses'], true) }}</td>
                <td class="text-right">
                    <span class="{{ $projection['net_cash_flow'] >= 0 ? 'positive' : 'negative' }}">
                        {{ getCurrencyAmount($projection['net_cash_flow'], true) }}
                    </span>
                </td>
                <td class="text-right">
                    <span class="{{ $projection['cumulative_cash_flow'] >= 0 ? 'positive' : 'negative' }}">
                        {{ getCurrencyAmount($projection['cumulative_cash_flow'], true) }}
                    </span>
                </td>
                <td class="text-center">
                    @if($projection['net_cash_flow'] >= 0)
                        <span class="positive">Positive</span>
                    @else
                        <span class="negative">Negative</span>
                    @endif
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <!-- Cash Flow Analysis -->
    <div class="section-title">Cash Flow Analysis</div>
    <table>
        <thead>
            <tr>
                <th>Metric</th>
                <th class="text-right">Value</th>
                <th>Analysis</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Average Monthly Income</td>
                <td class="text-right">{{ getCurrencyAmount(collect($data['monthly_projections'] ?? [])->avg('expected_income'), true) }}</td>
                <td>{{ collect($data['monthly_projections'] ?? [])->avg('expected_income') > 10000 ? 'Strong income projection' : 'Consider strategies to increase income' }}</td>
            </tr>
            <tr>
                <td>Average Monthly Expenses</td>
                <td class="text-right">{{ getCurrencyAmount(collect($data['monthly_projections'] ?? [])->avg('expected_expenses'), true) }}</td>
                <td>Monitor expense levels and optimize where possible</td>
            </tr>
            <tr>
                <td>Income Volatility</td>
                <td class="text-right">
                    @php
                        $incomes = collect($data['monthly_projections'] ?? [])->pluck('expected_income');
                        $avgIncome = $incomes->avg();
                        $volatility = $avgIncome > 0 ? ($incomes->max() - $incomes->min()) / $avgIncome * 100 : 0;
                    @endphp
                    {{ number_format($volatility, 1) }}%
                </td>
                <td>{{ $volatility > 50 ? 'High income volatility - consider diversifying revenue streams' : 'Stable income projection' }}</td>
            </tr>
            <tr>
                <td>Months with Negative Flow</td>
                <td class="text-right">{{ collect($data['monthly_projections'] ?? [])->where('net_cash_flow', '<', 0)->count() }}</td>
                <td>{{ $hasNegativeMonths ? 'Plan for cash flow gaps with credit facilities' : 'Consistent positive cash flow expected' }}</td>
            </tr>
        </tbody>
    </table>

    <!-- Recommendations -->
    <div class="section-title">Cash Flow Recommendations</div>
    <table>
        <thead>
            <tr>
                <th>Area</th>
                <th>Current Status</th>
                <th>Recommendation</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Overall Cash Position</td>
                <td>{{ $finalBalance >= 0 ? 'Positive' : 'Negative' }}</td>
                <td>
                    @if($finalBalance >= 0)
                        Consider investment opportunities for excess cash
                    @else
                        Implement cash flow improvement strategies immediately
                    @endif
                </td>
            </tr>
            <tr>
                <td>Income Acceleration</td>
                <td>{{ getCurrencyAmount(collect($data['monthly_projections'] ?? [])->sum('expected_income'), true) }} projected</td>
                <td>
                    @if(collect($data['monthly_projections'] ?? [])->avg('expected_income') < 5000)
                        Focus on increasing sales and faster invoice collection
                    @else
                        Maintain current income generation strategies
                    @endif
                </td>
            </tr>
            <tr>
                <td>Expense Management</td>
                <td>{{ number_format((collect($data['monthly_projections'] ?? [])->sum('expected_expenses') / collect($data['monthly_projections'] ?? [])->sum('expected_income')) * 100, 1) }}% of income</td>
                <td>
                    @php $expenseRatio = collect($data['monthly_projections'] ?? [])->sum('expected_income') > 0 ? (collect($data['monthly_projections'] ?? [])->sum('expected_expenses') / collect($data['monthly_projections'] ?? [])->sum('expected_income')) * 100 : 0; @endphp
                    @if($expenseRatio > 70)
                        Review and reduce expenses to improve cash flow
                    @elseif($expenseRatio > 50)
                        Monitor expenses closely and optimize where possible
                    @else
                        Excellent expense management
                    @endif
                </td>
            </tr>
        </tbody>
    </table>

    <!-- Risk Factors -->
    @if($hasNegativeMonths || $finalBalance < 0)
    <div class="section-title">Risk Factors & Mitigation</div>
    <table>
        <thead>
            <tr>
                <th>Risk Factor</th>
                <th>Impact</th>
                <th>Mitigation Strategy</th>
            </tr>
        </thead>
        <tbody>
            @if($hasNegativeMonths)
            <tr>
                <td>Negative Cash Flow Months</td>
                <td>{{ collect($data['monthly_projections'] ?? [])->where('net_cash_flow', '<', 0)->count() }} months affected</td>
                <td>Establish credit line, accelerate collections, defer non-essential expenses</td>
            </tr>
            @endif
            @if($finalBalance < 0)
            <tr>
                <td>Negative Final Balance</td>
                <td>{{ getCurrencyAmount($finalBalance, true) }} shortfall</td>
                <td>Increase sales efforts, extend payment terms with suppliers, consider financing</td>
            </tr>
            @endif
        </tbody>
    </table>
    @endif

    <div class="footer">
        <p>This report was generated automatically by the Invoice Management System</p>
        <p>{{ config('app.name') }} - Cash Flow Projections & Analysis</p>
        <p><strong>Note:</strong> Projections are based on current data and assumptions. Actual results may vary.</p>
    </div>
</body>
</html>
